#!/usr/bin/env python3
"""
LLM 8-K Analysis Project Setup Script
This script helps configure the project with API keys and settings.
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header():
    print("=" * 60)
    print("  LLM 8-K Analysis Project Setup")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is 3.8+"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required. Current version:", sys.version)
        return False
    print("✅ Python version:", sys.version.split()[0])
    return True

def setup_virtual_environment():
    """Create and activate virtual environment"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    print("📦 Creating virtual environment...")
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to create virtual environment")
        return False

def install_dependencies():
    """Install required packages"""
    print("📦 Installing dependencies...")
    
    # Determine pip path
    if os.name == 'nt':  # Windows
        pip_path = "venv/Scripts/pip"
    else:  # Unix/Linux/macOS
        pip_path = "venv/bin/pip"
    
    try:
        subprocess.run([pip_path, "install", "--upgrade", "pip"], check=True)
        subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        print("✅ Dependencies installed")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def create_env_file():
    """Interactive creation of .env file"""
    print("\n🔐 Setting up environment variables...")
    print("Please provide the following API keys and configuration:")
    print("(Press Enter to skip optional items)")
    print()
    
    env_vars = {}
    
    # Required API keys
    print("📱 REQUIRED: Telegram Bot Configuration")
    telegram_token = input("Telegram Bot Token: ").strip()
    if not telegram_token:
        print("❌ Telegram Bot Token is required!")
        return False
    env_vars['TELEGRAM_BOT_TOKEN'] = telegram_token
    
    chat_ids = input("Telegram Chat IDs (comma-separated): ").strip()
    if chat_ids:
        env_vars['TELEGRAM_CHAT_IDS'] = chat_ids
    
    print("\n💰 REQUIRED: Financial Data API")
    fmp_key = input("Financial Modeling Prep API Key: ").strip()
    if not fmp_key:
        print("❌ Financial Modeling Prep API Key is required!")
        return False
    env_vars['FINANCIAL_MODELING_PREP_API_KEY'] = fmp_key
    
    # Optional API keys
    print("\n📈 OPTIONAL: Trading API (for automated trading)")
    alpaca_key = input("Alpaca API Key (optional): ").strip()
    if alpaca_key:
        env_vars['ALPACA_API_KEY'] = alpaca_key
        alpaca_secret = input("Alpaca API Secret: ").strip()
        if alpaca_secret:
            env_vars['ALPACA_API_SECRET'] = alpaca_secret
    
    print("\n📊 OPTIONAL: Additional Market Data APIs")
    twelve_key = input("Twelve Data API Key (optional): ").strip()
    if twelve_key:
        env_vars['TWELVEDATA_API_KEY'] = twelve_key
    
    polygon_key = input("Polygon API Key (optional): ").strip()
    if polygon_key:
        env_vars['POLYGON_API_KEY'] = polygon_key
    
    # Write .env file
    with open('.env', 'w') as f:
        f.write("# LLM 8-K Analysis Environment Variables\n")
        f.write("# Generated by setup.py\n\n")
        
        f.write("# Required API Keys\n")
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")
    
    print("✅ .env file created successfully")
    return True

def check_ollama():
    """Check if Ollama is installed and running"""
    print("\n🤖 Checking Ollama installation...")
    
    try:
        result = subprocess.run(["ollama", "--version"], 
                              capture_output=True, text=True, check=True)
        print("✅ Ollama is installed:", result.stdout.strip())
        
        # Check if the required model is available
        result = subprocess.run(["ollama", "list"], 
                              capture_output=True, text=True, check=True)
        
        if "deepseek-r1:14b" in result.stdout:
            print("✅ deepseek-r1:14b model is available")
        else:
            print("⚠️  deepseek-r1:14b model not found")
            pull_model = input("Would you like to pull the model now? (y/N): ").strip().lower()
            if pull_model == 'y':
                print("📥 Pulling deepseek-r1:14b model (this may take a while)...")
                subprocess.run(["ollama", "pull", "deepseek-r1:14b"], check=True)
                print("✅ Model pulled successfully")
        
        return True
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Ollama not found or not running")
        print("Please install Ollama from https://ollama.ai/")
        print("The AI analysis features will not work without Ollama")
        return False

def test_configuration():
    """Test if the configuration works"""
    print("\n🧪 Testing configuration...")
    
    try:
        # Test importing config
        import config
        print("✅ Configuration loaded successfully")
        
        # Test API keys
        if config.TELEGRAM_BOT_TOKEN:
            print("✅ Telegram Bot Token configured")
        else:
            print("❌ Telegram Bot Token missing")
            
        if config.FINANCIAL_MODELING_PREP_API_KEY:
            print("✅ Financial Modeling Prep API Key configured")
        else:
            print("❌ Financial Modeling Prep API Key missing")
            
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Main setup function"""
    print_header()
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    # Setup virtual environment
    if not setup_virtual_environment():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Create .env file
    if not create_env_file():
        return False
    
    # Check Ollama
    check_ollama()
    
    # Test configuration
    if not test_configuration():
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Activate the virtual environment:")
    if os.name == 'nt':
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    print("2. Run the monitor: python monitor.py")
    print("3. Or run the analyzer: python general_analyzer.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
