#!/usr/bin/env python3
"""
LLM 8-K Analysis Project Runner
Simple script to run different components of the project.
"""

import os
import sys
import subprocess
from pathlib import Path

def print_menu():
    print("=" * 50)
    print("  LLM 8-K Analysis Project")
    print("=" * 50)
    print()
    print("Choose what to run:")
    print("1. Stock Monitor (recommended for beginners)")
    print("2. 8-K Filing Analyzer")
    print("3. Trading Bot (⚠️  executes real trades!)")
    print("4. Check system status")
    print("5. Exit")
    print()

def check_venv():
    """Check if we're in a virtual environment"""
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Not running in a virtual environment")
        print("Please activate the virtual environment first:")
        if os.name == 'nt':
            print("   venv\\Scripts\\activate")
        else:
            print("   source venv/bin/activate")
        return False
    return True

def check_env_file():
    """Check if .env file exists"""
    if not Path('.env').exists():
        print("❌ .env file not found!")
        print("Please run setup.py first to configure the project")
        return False
    return True

def run_monitor():
    """Run the stock monitor"""
    print("🔍 Starting Stock Monitor...")
    print("This will monitor stock price movements and send alerts.")
    print("Press Ctrl+C to stop.")
    print()
    
    try:
        subprocess.run([sys.executable, "monitor.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Monitor stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Monitor failed: {e}")

def run_analyzer():
    """Run the 8-K analyzer"""
    print("📊 Starting 8-K Filing Analyzer...")
    print("This will analyze recent SEC 8-K filings for investment events.")
    print()
    
    max_filings = input("Max filings to process (default: 5): ").strip()
    if not max_filings:
        max_filings = "5"
    
    try:
        subprocess.run([sys.executable, "general_analyzer.py", 
                       "--max-filings-to-process", max_filings], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Analyzer failed: {e}")

def run_trading_bot():
    """Run the trading bot"""
    print("⚠️  WARNING: TRADING BOT")
    print("This will execute real trades based on analysis signals!")
    print()
    
    confirm = input("Are you sure you want to continue? Type 'YES' to confirm: ").strip()
    if confirm != "YES":
        print("Trading bot cancelled.")
        return
    
    print("🤖 Starting Trading Bot...")
    print("Press Ctrl+C to stop.")
    print()
    
    try:
        subprocess.run([sys.executable, "trade_8k_signals.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Trading bot stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Trading bot failed: {e}")

def check_status():
    """Check system status"""
    print("🔍 System Status Check")
    print("=" * 30)
    
    # Check Python version
    print(f"Python version: {sys.version.split()[0]}")
    
    # Check virtual environment
    if check_venv():
        print("✅ Virtual environment: Active")
    else:
        print("❌ Virtual environment: Not active")
    
    # Check .env file
    if check_env_file():
        print("✅ Configuration: .env file found")
    else:
        print("❌ Configuration: .env file missing")
    
    # Check Ollama
    try:
        result = subprocess.run(["ollama", "--version"], 
                              capture_output=True, text=True, check=True)
        print("✅ Ollama: Installed")
    except:
        print("❌ Ollama: Not found")
    
    # Check key packages
    try:
        import pandas
        print("✅ Pandas: Available")
    except ImportError:
        print("❌ Pandas: Missing")
    
    try:
        import yfinance
        print("✅ YFinance: Available")
    except ImportError:
        print("❌ YFinance: Missing")
    
    try:
        import telegram
        print("✅ Telegram Bot: Available")
    except ImportError:
        print("❌ Telegram Bot: Missing")
    
    print()

def main():
    """Main function"""
    while True:
        print_menu()
        
        try:
            choice = input("Enter your choice (1-5): ").strip()
            print()
            
            if choice == "1":
                if check_venv() and check_env_file():
                    run_monitor()
            elif choice == "2":
                if check_venv() and check_env_file():
                    run_analyzer()
            elif choice == "3":
                if check_venv() and check_env_file():
                    run_trading_bot()
            elif choice == "4":
                check_status()
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-5.")
            
            print()
            input("Press Enter to continue...")
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
