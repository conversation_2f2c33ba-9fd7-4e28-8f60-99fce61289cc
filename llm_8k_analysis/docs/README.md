# LLM 8-K Analysis Project

An AI-powered system for analyzing SEC 8-K filings and automated stock trading based on cryptocurrency and investment-related material events.

## 🚀 Features

- **AI-Powered 8-K Analysis**: Uses local LLM (Ollama) to analyze SEC filings for cryptocurrency and investment disclosures
- **Stock Monitoring**: Real-time price movement detection using statistical analysis (Z-scores)
- **Automated Trading**: Integration with Alpaca API for automated trading based on analysis signals
- **Telegram Notifications**: Real-time alerts with charts and analysis summaries
- **Secure Configuration**: Environment variable-based configuration with no hardcoded credentials

## 📁 Project Structure

```
llm_8k_analysis/
├── ai_tools/                     # AI analysis modules
│   ├── ai_utils.py              # Basic AI utilities
│   └── ai_utils_general.py      # Advanced AI analysis
├── utils/                        # Utility functions
│   ├── alpaca_trade.py          # Trading utilities
│   ├── price_api.py             # Price data APIs
│   ├── stock_chart.py           # Chart generation
│   ├── stock_data.py            # Stock data analysis
│   └── telegram_bot.py          # Telegram notifications
├── data/                         # Data storage
│   ├── charts/                  # Generated stock charts
│   └── *.parquet               # Analysis results
├── logs/                         # Application logs
├── config.py                     # Configuration management
├── edgar.py                      # SEC EDGAR API interface
├── general_analyzer.py           # Main 8-K filing analyzer
├── monitor.py                    # Stock price monitoring
├── trade_8k_signals.py           # Trading automation
├── setup.py                      # Interactive setup script
├── run.py                        # Project runner
├── requirements.txt              # Python dependencies
└── .env                          # Environment variables (created by setup)
```

## 🛠️ Quick Setup

### Prerequisites

- Python 3.8+
- [Ollama](https://ollama.ai/) (for AI analysis)
- API keys for:
  - Telegram Bot
  - Financial Modeling Prep
  - Alpaca Trading (optional)
  - Twelve Data (optional)

### 1. Interactive Setup

```bash
# Navigate to the project directory
cd llm_8k_analysis

# Run the interactive setup script
python setup.py
```

This script will:
- Create a Python virtual environment
- Install all dependencies
- Guide you through API key configuration
- Create the .env file automatically
- Check for Ollama and required models
- Test the configuration

### 2. Quick Start

```bash
# Activate the virtual environment
source venv/bin/activate  # On macOS/Linux
# or
venv\Scripts\activate     # On Windows

# Run the project
python run.py
```

The run script provides an interactive menu to:
- Monitor stocks (recommended for beginners)
- Analyze 8-K filings
- Run trading bot (⚠️ executes real trades!)
- Check system status

## 🎯 Usage Modes

### 1. Monitor Only (Recommended for beginners)
```bash
python -m src.llm_8k_analysis.monitor
```
- Monitors stock price movements
- Sends alerts for unusual activity
- No trading involved

### 2. 8-K Filing Analysis
```bash
python -m src.llm_8k_analysis.general_analyzer --max-filings-to-process 5
```
- Analyzes recent SEC 8-K filings
- Identifies cryptocurrency and investment events
- Stores results for monitoring

### 3. Automated Trading
```bash
python -m src.llm_8k_analysis.trade_8k_signals
```
- **⚠️ WARNING**: This executes real trades!
- Requires Alpaca API configuration
- Uses analysis results to make trading decisions

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `TELEGRAM_BOT_TOKEN` | Telegram bot token | Yes | - |
| `FINANCIAL_MODELING_PREP_API_KEY` | Financial data API | Yes | - |
| `ALPACA_API_KEY` | Alpaca trading API key | No | - |
| `ALPACA_API_SECRET` | Alpaca trading API secret | No | - |
| `ALPACA_PAPER_TRADING` | Use paper trading | No | `true` |
| `OLLAMA_MODEL` | Ollama model to use | No | `deepseek-r1:14b` |
| `SEND_TO_TELEGRAM` | Enable Telegram notifications | No | `false` |
| `TESTING` | Enable testing mode | No | `true` |
| `Z_SCORE_THRESHOLD` | Price movement threshold | No | `2.0` |
| `CYCLE_DURATION` | Monitoring cycle duration (seconds) | No | `1800` |

### Telegram Setup

1. Create a bot with [@BotFather](https://t.me/botfather)
2. Get your bot token
3. Add the token to your `.env` file
4. Add your chat ID to `TELEGRAM_CHAT_IDS` (comma-separated for multiple users)

### Ollama Setup

1. Install Ollama from [ollama.ai](https://ollama.ai/)
2. Pull the required model:
   ```bash
   ollama pull deepseek-r1:14b
   ```

## 🧪 Development

### Setup Development Environment
```bash
./scripts/dev_setup.sh
```

This installs additional tools:
- pytest (testing)
- black (code formatting)
- flake8 (linting)
- mypy (type checking)
- jupyter (notebooks)

### Running Tests
```bash
pytest
```

### Code Formatting
```bash
black src/
```

### Linting
```bash
flake8 src/
```

## 📊 How It Works

1. **Data Collection**: Monitors SEC EDGAR database for new 8-K filings
2. **AI Analysis**: Uses local LLM to analyze filings for crypto/investment events
3. **Signal Generation**: Identifies material events that could affect stock prices
4. **Price Monitoring**: Tracks stock price movements using statistical analysis
5. **Notifications**: Sends alerts via Telegram with charts and analysis
6. **Trading** (Optional): Executes trades based on analysis signals

## ⚠️ Important Notes

- **Testing Mode**: The system runs in testing mode by default. Set `TESTING=false` for live operations
- **Paper Trading**: Alpaca trading uses paper trading by default. Set `ALPACA_PAPER_TRADING=false` for live trading
- **Rate Limits**: The system respects API rate limits to avoid being blocked
- **Data Storage**: Analysis results are stored locally in the `data/` directory

## 🔒 Security

- All sensitive data is stored in environment variables
- `.env` file is excluded from version control
- API keys are never hardcoded in the source code
- Logs are stored locally and not transmitted

## 📝 License

This project is for educational and research purposes. Please ensure compliance with all applicable regulations when using for trading.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the logs in the `logs/` directory
2. Verify your `.env` configuration
3. Ensure all required services (Ollama, APIs) are running
4. Review the error messages for specific guidance
