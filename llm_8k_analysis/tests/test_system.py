#!/usr/bin/env python3
"""
System Test Script for LLM 8-K Analysis Project
Tests all major components to ensure they work properly.
"""

import sys
import traceback

def test_configuration():
    """Test configuration loading"""
    print("🔧 Testing Configuration...")
    try:
        import config
        print(f"✅ Configuration loaded")
        print(f"✅ Telegram Bot: {'Configured' if config.TELEGRAM_BOT_TOKEN else 'Missing'}")
        print(f"✅ Financial API: {'Configured' if config.FINANCIAL_MODELING_PREP_API_KEY else 'Missing'}")
        print(f"✅ Chat IDs: {config.get_telegram_chat_ids()}")
        print(f"✅ Testing mode: {config.TESTING}")
        return True
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        return False

def test_stock_data():
    """Test stock data fetching"""
    print("\n📊 Testing Stock Data...")
    try:
        import yfinance as yf
        ticker = yf.Ticker('AAPL')
        info = ticker.info
        print(f"✅ Stock data fetch successful")
        print(f"✅ AAPL: {info.get('shortName', 'Apple Inc.')} - ${info.get('currentPrice', 'N/A')}")
        return True
    except Exception as e:
        print(f"❌ Stock data failed: {e}")
        return False

def test_telegram_bot():
    """Test Telegram bot utilities"""
    print("\n📱 Testing Telegram Bot...")
    try:
        from utils.telegram_bot import TelegramBot
        print("✅ Telegram bot utilities imported")
        return True
    except Exception as e:
        print(f"❌ Telegram bot failed: {e}")
        return False

def test_ai_tools():
    """Test AI analysis tools"""
    print("\n🤖 Testing AI Tools...")
    try:
        import ai_tools.ai_utils
        print("✅ AI utilities imported")

        # Test Ollama connection
        import requests
        from config import OLLAMA_API_URL

        # Simple ping test
        try:
            response = requests.get(OLLAMA_API_URL.replace('/api/generate', ''), timeout=5)
            if response.status_code == 200:
                print("✅ Ollama server is running")
            else:
                print("⚠️  Ollama server may not be running")
        except:
            print("⚠️  Could not connect to Ollama server")

        return True
    except Exception as e:
        print(f"❌ AI tools failed: {e}")
        return False

def test_edgar():
    """Test EDGAR API interface"""
    print("\n📋 Testing EDGAR Interface...")
    try:
        import edgar
        print("✅ EDGAR module imported")
        return True
    except Exception as e:
        print(f"❌ EDGAR failed: {e}")
        return False

def test_monitor():
    """Test monitor module"""
    print("\n🔍 Testing Monitor Module...")
    try:
        import monitor
        print("✅ Monitor module imported")
        return True
    except Exception as e:
        print(f"❌ Monitor failed: {e}")
        return False

def test_analyzer():
    """Test general analyzer"""
    print("\n📊 Testing General Analyzer...")
    try:
        import general_analyzer
        print("✅ General analyzer imported")
        return True
    except Exception as e:
        print(f"❌ General analyzer failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("  LLM 8-K Analysis System Test")
    print("=" * 60)
    
    tests = [
        test_configuration,
        test_stock_data,
        test_telegram_bot,
        test_ai_tools,
        test_edgar,
        test_monitor,
        test_analyzer
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"  Test Results: {passed}/{total} passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! Your system is ready to use.")
        print("\nNext steps:")
        print("1. Run the monitor: python monitor.py")
        print("2. Run the analyzer: python general_analyzer.py")
        print("3. Use the interactive runner: python run.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
