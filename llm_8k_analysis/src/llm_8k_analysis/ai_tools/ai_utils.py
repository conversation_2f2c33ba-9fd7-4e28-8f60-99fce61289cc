import requests
from pydantic import BaseModel

OLLAMA_API_URL = "http://localhost:11434/api/generate"
OLLAMA_MODEL = "qwen3:8b"

from enum import Enum
from pydantic import BaseModel, Field
from typing import Dict, Optional

class Recommendation(str, Enum):
    material_event = "material_event"
    monitor_only = "monitor_only"

class CoinInfo(BaseModel):
    activity: str
    impact: str

class Crypto8KSimpleAnalysis(BaseModel):
    contains_cryptocurrencies: bool = Field(...)
    coins: Dict[str, CoinInfo]  # e.g., {"BTC": CoinInfo(...), "ETH": CoinInfo(...)}
    recommendation: Recommendation
    notes: str  # one-sentence summary
    ticker: str = None
    title: str = None
    cik_str: str = None


def evaluator(system_prompt, user_prompt) -> dict:
    """
    Sends a prompt to the Ollama API for scoring resume fit.
    
    Args:
        system_prompt (str): The system prompt
        user_prompt (str): The user prompt
        
    Returns:
        dict: API response containing the fit score and other details
    """
    
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": OLLAMA_MODEL,
        "system": system_prompt,
        "prompt": user_prompt,
        "stream": False,
        "format": Crypto8KSimpleAnalysis.model_json_schema(),
         "options": {
            "num_ctx": 8096
        }
    }

    try:
        response = requests.post(OLLAMA_API_URL, headers=headers, json=payload, timeout=60)
        response.raise_for_status()  # Raises an HTTPError for bad responses
        return response.json()
    except requests.exceptions.Timeout:
        print("Request timed out after 15 seconds")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return None
    

def create_crypto_8k_analysis_prompt(filing_text: str) -> dict:
    """
    Creates a detailed prompt for evaluating an 8‑K for crypto-related disclosures.
    """
    system_prompt = """You are a senior securities analyst. Read a Form 8-K and focus **only** on major cryptocurrencies:
- Bitcoin (BTC)
- Ethereum (ETH)
- XRP (XRP)
- Solana (SOL)
- NEAR Protocol (NEAR)

Identify any disclosures mentioning these major cryptocurrencies.

Summarize:
- What activity is described for each coin (e.g., holding, investing)?
- Any financial impact mentioned (amounts, losses, gains).
- Overall assessment: material or not.
"""

    user_prompt = f"""8-K TEXT:
{filing_text}

TASK:
1. List which of BTC, ETH, XRP, SOL, NEAR are mentioned.
2. Set `contains_cryptocurrencies` to true or false.
3. If true, for each mentioned coin, say:
   - What the company did (activity)
   - Financial impact (e.g. “$5M loss”)
4. Give a recommendation:
   - material_event (if significant exposure)
   - monitor_only (if minor)
Return as JSON:

{{
 "contains_cryptocurrencies": <true|false>,
 "coins": {{
   "BTC": {{"activity": "...", "impact": "..."}},
   "ETH": {{"activity": "...", "impact": "..."}},
    ...
 }},
 "recommendation": "material_event|monitor_only",
 "notes": "A brief summary of the analysis, the activity described, quantity of investment, and financial impact. It should be three-sentence long."
}}

Return output in JSON matching the Pydantic model below.
"""

    return {"system": system_prompt, "user": user_prompt}


def ai_8k_evaluator(filing_text: str) -> Optional[Crypto8KSimpleAnalysis]:
    prompt = create_crypto_8k_analysis_prompt(filing_text)
    attempts = 3
    while attempts > 0:
        try:
            evaluator_output = evaluator(prompt["system"], prompt["user"])
            response = Crypto8KSimpleAnalysis.model_validate_json(evaluator_output["response"])
            if response is not None:
                return response
        except Exception as e:
            pass
        attempts -= 1

    return None
