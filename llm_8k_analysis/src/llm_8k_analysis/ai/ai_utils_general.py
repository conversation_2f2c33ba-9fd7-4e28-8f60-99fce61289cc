import requests
from pydantic import BaseModel
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import OLLAMA_API_URL, OLLAMA_MODEL

from enum import Enum
from typing import Dict, List, Optional
from pydantic import BaseModel, Field

from typing import Dict, List, Optional
from enum import Enum
from pydantic import BaseModel, Field, field_serializer

class Recommendation(str, Enum):
    material_event = "material_event"
    monitor_only = "monitor_only"

class CoinInfo(BaseModel):
    activity: str
    impact: str

class NonCryptoEvent(BaseModel):
    description: str
    impact: str

class Expanded8KAnalysis(BaseModel):
    contains_events: bool = Field(..., description="True if any crypto or non‑crypto investment/gain events are found")
    crypto_coins: Dict[str, CoinInfo] = Field(default_factory=dict, description="Crypto coin activity and impact")
    non_crypto_events: List[NonCryptoEvent] = Field(default_factory=list, description="List of non‑crypto investment/gain events")
    recommendation: Recommendation
    notes: str  # three-sentence summary
    ticker: Optional[str] = None
    title: Optional[str] = None
    cik_str: Optional[str] = None

    @field_serializer('non_crypto_events', when_used='json')
    def serialize_non_crypto(self, events: List[NonCryptoEvent], _info):
        if not events:
            return ""
        return "; ".join(f"{e.description} ({e.impact})" for e in events)

    @field_serializer('crypto_coins', when_used='json')
    def serialize_crypto(self, coins: Dict[str, CoinInfo], _info):
        if not coins:
            return ""
        return "; ".join(f"{coin}: {info.activity} ({info.impact})" for coin, info in coins.items())


def evaluator(system_prompt, user_prompt) -> dict:
    """
    Sends a prompt to the Ollama API for scoring resume fit.
    
    Args:
        system_prompt (str): The system prompt
        user_prompt (str): The user prompt
        
    Returns:
        dict: API response containing the fit score and other details
    """
    
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": OLLAMA_MODEL,
        "system": system_prompt,
        "prompt": user_prompt,
        "stream": False,
        "format": Expanded8KAnalysis.model_json_schema(),
         "options": {
            "num_ctx": 40960  # Increased context size for more detailed analysis
        }
    }

    try:
        response = requests.post(OLLAMA_API_URL, headers=headers, json=payload, timeout=300)
        response.raise_for_status()  # Raises an HTTPError for bad responses
        return response.json()
    except requests.exceptions.Timeout:
        print("Request timed out after 180 seconds")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return None
    
def create_crypto_and_investment_8k_analysis_prompt(filing_text: str) -> dict:
    """
    Creates a detailed prompt for evaluating an 8-K for disclosures about:
      - Major cryptocurrencies (only new investment or gains)
      - Material non-crypto new investments or financial gains
    """
    system_prompt = """You are a highly skilled and meticulous senior securities analyst with expertise in financial disclosures. Your task is to carefully read a Form 8-K filing and identify specific types of financial events.

**Focus Exclusively On:**

**1. Major Cryptocurrencies (New Investment or Gains ONLY):**
   - Identify disclosures related investments in or realized financial gains from the following major cryptocurrencies:
     - Bitcoin (BTC)
     - Ethereum (ETH)
     - XRP (XRP)
     - Solana (SOL)
     - NEAR Protocol (NEAR)
   - **Crucially, ignore and do not report any disclosures related to crypto losses, impairments, declines in value.**
   - Examples of what to capture: "acquired 100 BTC for $X million," "realized a $Y million gain from the sale of ETH."

**2. Material Non-Crypto New Investments or Financial Gains (ONLY):**
   - Identify disclosures related *only* to **material new investments** or the **recognition of material financial gains** from non-crypto assets or activities.
   - Examples of what constitutes a material new investment:
     - Acquisition of equity stakes in other companies (e.g., acquiring a minority or majority interest in a startup).
     - Acquisition of significant assets or business segments.
     - Formation of new joint ventures where the company makes a substantial capital contribution.
   - Examples of what constitutes recognition of a material financial gain:
     - Gains from the sale or divestiture of significant assets, subsidiaries, or business units.
     - Significant profits from a specific, non-routine financial transaction (e.g., settlement gain, specific investment gain).
   - **Crucially, ignore and do not report:**
     - Routine operational revenues or profits.
     - Any losses, impairments, write-downs, or negative financial impacts.
     - Minor, immaterial, or ordinary course of business transactions.

**Summary Requirements:**

- For each identified major cryptocurrency (BTC, ETH, XRP, SOL, NEAR) associated with a **new investment or gain**, clearly describe the activity and its financial impact.
- For each identified material non-crypto new investment or gain event, clearly describe its nature and magnitude.
- Provide an overall assessment of the materiality of any identified events, justifying your recommendation.
"""

    user_prompt = f"""8-K TEXT:
{filing_text}

TASK:
Based on the provided 8-K text, perform the following analysis and provide your output in the specified JSON format.

1.  **Event Detection:**
    * Determine if the filing contains any mentions of BTC, ETH, XRP, SOL, or NEAR associated *specifically* with **new investments or realized gains**.
    * Determine if the filing contains any **non-crypto material events** involving **new investments or recognition of financial gains**.

2.  **`contains_events` Flag:**
    * Set `contains_events` to `true` if *any* event from either category above (crypto new investment/gain or non-crypto material new investment/gain) is detected. Otherwise, set to `false`.

3.  **Detailed Event Information (If `contains_events` is `true`):**
    * **`crypto_coins` Object:**
        * List *only* those major crypto coins (BTC, ETH, XRP, SOL, NEAR) that are explicitly associated with **new investment or realized gains**.
        * For each such coin, provide:
            * `"activity"`: A concise description of the new investment or gain (e.g., "Acquired 10 BTC as part of a treasury diversification strategy", "Realized $2 million gain from sale of ETH holdings").
            * `"impact"`: The precise financial impact (e.g., "$1.5 million investment", "$600K gain", "Value of investment $X"). State the currency (e.g., USD).
    * **`non_crypto_events` Array:**
        * For each detected **material non-crypto new investment or financial gain event**, provide an object with:
            * `"description"`: A clear and concise description of the event (e.g., "Acquired 20% equity stake in startup 'InnovateCo'", "Recognized $5 million gain from divestiture of subsidiary 'OldBiz Inc.'").
            * `"impact"`: The precise financial impact (e.g., "$50 million investment", "$3 million gain recognized", "Purchase price of $X"). State the currency (e.g., USD).

4.  **`recommendation`:**
    * Set to `"material_event"` if any identified event (crypto or non-crypto) is significant in size or qualitative importance, warranting investor attention.
    * Otherwise, set to `"monitor_only"`. Justify your choice briefly in the `notes`.

5.  **`notes`:**
    * Provide a concise, three-sentence summary covering the key activities identified, their respective categories (crypto/non-crypto), and the overall financial magnitudes involved. This should also briefly justify the `recommendation`.

Return the output strictly in the JSON format matching the Pydantic model below. Ensure all keys are present, even if their values are empty arrays or objects when no events are found.

```json
{{
  "contains_events": true,
  "crypto_coins": {{
    "BTC": {{"activity": "Acquired 10 BTC for treasury", "impact": "$1.5M investment"}},
    "ETH": {{"activity": "Realized gain from sale of ETH holdings", "impact": "$600K gain"}}
  }},
  "non_crypto_events": [
    {{
      "description": "Acquired 20% equity in startup X",
      "impact": "$5M investment"
    }},
    {{
      "description": "Recognized gain from sale of subsidiary Y",
      "impact": "$3M gain recognized"
    }}
  ],
  "recommendation": "material_event",
  "notes": "The filing discloses a new investment or significant gain from crypto, alongside an equity acquisition and a subsidiary divestiture. These items represent material financial changes for the company, warranting a material event recommendation."
}}
```
"""
    return {"system": system_prompt, "user": user_prompt}



def ai_8k_evaluator(filing_text: str) -> Optional[Expanded8KAnalysis]:
    prompt = create_crypto_and_investment_8k_analysis_prompt(filing_text)
    attempts = 3
    while attempts > 0:
        try:
            evaluator_output = evaluator(prompt["system"], prompt["user"])
            response = Expanded8KAnalysis.model_validate_json(evaluator_output["response"])
            if response is not None:
                return response
        except Exception as e:
            print(e.__traceback__.tb_frame.f_code.co_filename + ":" + str(e.__traceback__.tb_lineno) + " " + e.__traceback__.tb_frame.f_code.co_name)
        attempts -= 1

    return None

if __name__ == "__main__":
    # Example usage
    filing_summary_crypto = """SharpLink Gaming, Inc. (Report Date: 2025-08-10) entered a registered direct offering
    selling 18,382,353 common shares at $21.76 each for gross proceeds of about $400M.
    Proceeds will be used primarily to acquire Ether (ETH) and for working capital.
    Agreements include a Purchase Agreement with institutional investors and a Placement Agent Agreement
    with A.G.P./Alliance Global Partners (2.5% fee) with Cantor Fitzgerald as financial advisor.
    Expected closing: 2025-08-12.
    Exhibits: 5.1 Opinion of Thompson Hine LLP, 10.1 Securities Purchase Agreement, 10.2 Placement Agent Agreement,
    23.1 Consent, 99.1 Press Release, 104 Cover Page Interactive Data File.
    """

    filing_summary_non_crypto = """Compass Therapeutics, Inc. (Report Date: 2025-08-12) entered an underwritten public offering
    of 33,290,000 common shares at $3.00 and 6,710,000 pre-funded warrants at $2.9999 (exercise $0.0001),
    with an option for 6,000,000 additional shares.
    Gross proceeds expected: $112.5M (up to $129.5M with option).
    Proceeds to fund commercial readiness, research and clinical development, and general corporate purposes.
    Agreements include an Underwriting Agreement with Jefferies LLC, Piper Sandler & Co., and Guggenheim Securities, LLC.
    Expected closing: 2025-08-14.
    Exhibits: 1.1 Underwriting Agreement, 4.1 Pre-Funded Warrant Form, 5.1 Opinion of Goodwin Procter LLP,
    23.1 Consent, 99.1 Launch Press Release, 99.2 Pricing Press Release, 104 Cover Page Interactive Data File.
    """

    print(ai_8k_evaluator(filing_summary_crypto))
    print(ai_8k_evaluator(filing_summary_non_crypto))