import logging
import os
import pathlib
import toml
import json

# Path Configuration
CURRENT_DIRECTORY = os.path.abspath(os.path.dirname(__file__))
ROOT_DIRECTORY = os.path.abspath(os.path.join(CURRENT_DIRECTORY, os.pardir))
DATA_DIRECTORY = os.path.join(ROOT_DIRECTORY, "data")
STRATEGY_RAW_DATA_FOLDER = os.path.join(DATA_DIRECTORY, 'triggered')
EDGAR_ALREADY_NOTIFIED_FILE = os.path.join(DATA_DIRECTORY, 'edgar_notified.json')
EDGAR_RAW_DATA_FOLDER = os.path.join(DATA_DIRECTORY, 'edgar_raw_data')
EDGAR_RAW_DATA_FILE_PATH = EDGAR_RAW_DATA_FOLDER + '/{}_{}.json'


# Pool configuration
SCRAPING_FREQUENCY_24h = 24*60*60  # 24 hours
SCRAPING_FREQUENCY_12h = 12*60*60  # 12 hours
SCRAPING_FREQUENCY_6h = 6*60*60  # 6 hours
SCRAPING_FREQUENCY_3h = 3*60*60  # 3 hours
SCRAPING_FREQUENCY_2h = 2*60*60  # 2 hours
SCRAPING_FREQUENCY_1h = 60*60  # 1 hour
SCRAPING_FREQUENCY_30m = 30*60  # 30 minutes
SCRAPING_FREQUENCY_15m = 15*60  # 15 minutes
SCRAPING_FREQUENCY_10m = 10*60  # 10 minutes
SCRAPING_FREQUENCY_5m = 5*60  # 5 minutes
SCRAPING_FREQUENCY_1m = 60  # 1 minute
SCRAPING_FREQUENCY_30s = 30  # 30 seconds
SCRAPING_FREQUENCY_5s = 5  # 5 seconds
SCRAPING_FREQUENCY_1s = 1  # 1 second

# Yahoo Finance API
YFINANCE_MAX_RATE_PER_SECOND = 3
# pool configuration  
EDGAR_MAX_RATE_PER_SECOND = 10
EDGAR_COMPANY_FACTS_URL = 'https://data.sec.gov/api/xbrl/companyfacts/CIK{:>010s}.json'
EDGAR_COMPANY_TICKERS_URL = 'https://www.sec.gov/files/company_tickers.json'
EDGAR_COMPANY_SUBMISSIONS_URL = 'https://data.sec.gov/submissions/CIK{:>010s}.json'
EDGAR_FILING_DETAILS_URL = 'https://www.sec.gov/cgi-bin/browse-edgar?action=getcurrent&CIK=&type={}&company=&dateb=&owner=include&start=0&count=1000&output=atom'
WEEK_LENGTH_IN_DAYS = 7
EDGAR_REQUEST_TIMEOUT = 5

# Financial Modeling Prep API
FINANCIAL_MODELING_PREP_API_KEY = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
FINANCIAL_MODELING_PREP_API_STOCK_SPLIT = 'https://financialmodelingprep.com/api/v3/historical-price-full/stock_split/{}?apikey=' + FINANCIAL_MODELING_PREP_API_KEY

# Telegram configuration
TELEGRAM_BOT_TOKEN = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'

# Strategy configuration
PERCENTAGE_CHANGE_THRESHOLD = 50

PERCENTAGE_CHANGE_THRESHOLD_EPS = PERCENTAGE_CHANGE_THRESHOLD

PERCENTAGE_CHANGE_THRESHOLD_CASHFLOW = PERCENTAGE_CHANGE_THRESHOLD
OPERATING_CASHFLOW_THRESHOLD = 5e7 # Fifty million

PERCENTAGE_CHANGE_THRESHOLD_NETINCOME = PERCENTAGE_CHANGE_THRESHOLD
INCOME_THRESHOLD = 5e7 # Fifty million


class TaxonomyConfig:
    def __init__(self, config_file):
        with open(config_file, "r", -1, "UTF-8") as f:
            configuration = toml.load(f)
            for k, v in configuration.items():
                setattr(self, k, v)
    def __str__(self):
        return json.dumps(self.__dict__, sort_keys=True, indent=4)
    def __getitem__(self, key):
        return self.__dict__[key] 
    def keys(self):
        return self.__dict__.keys()
    
# Get the directory of the current file
current_dir = os.path.dirname(os.path.abspath(__file__))
