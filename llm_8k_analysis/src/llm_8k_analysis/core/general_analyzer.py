import logging
import os
import re
import time
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any

import argparse
import pandas as pd
from bs4 import BeautifulSoup
from tqdm import tqdm

from edgar import Edgar, EdgarRawData
from ai_tools.ai_utils_general import *
from zoneinfo import ZoneInfo
from utils.telegram_bot import TelegramBot
from config import TELEGRAM_BOT_TOKEN, SEND_TO_TELEGRAM, DATA_DIRECTORY, STOCKS_DB_FILE, RESULTS_DB_FILE, LOOKBACK_DAYS, REQUEST_DELAY
from utils.stock_data import financial_summary

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('8k_general_analyzer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration constants
class Config:
    SEND_TO_TELEGRAM = False
    DB_DIR = Path("db")
    STOCKS_DB_FILE = "general_stocks_df.parquet"
    RESULTS_DB_FILE = "general_results.parquet"
    SLEEP_INTERVAL = 60  # seconds
    LOOKBACK_DAYS = 30
    REQUEST_DELAY = 1.0  # seconds between requests to avoid rate limiting

# Ensure database directory exists
Config.DB_DIR.mkdir(exist_ok=True)


def parse_arguments():
    parser = argparse.ArgumentParser(description="Parse all companies")
    parser.add_argument('--all', action='store_true', help='Whether to parse all companies or only recent filings. Default is True.')
    parser.add_argument('--max-filings-to-process', type=int, default=10, help='Maximum number of filings to process per company. Default is 10.')
    return parser.parse_args()

class TextProcessor:
    """Optimized text processing utilities."""
    
    @staticmethod
    def clean_html_text(text: str) -> str:
        """
        Removes HTML tags, attributes, scripts/styles/comments, and returns plain text.
        Optimized version with better regex patterns.
        """
        if not text or not isinstance(text, str):
            return ""
        
        # Remove XML declarations, processing instructions, and comments in one pass
        text = re.sub(r"<\?.*?\?>|<!--.*?-->", "", text, flags=re.DOTALL)
        
        # Parse with BeautifulSoup
        soup = BeautifulSoup(text, "html.parser")
        
        # Remove script and style elements
        for tag in soup(["script", "style", "head", "meta", "link"]):
            tag.decompose()
        
        # Get plain text with better separator handling
        plain_text = soup.get_text(separator=" ", strip=True)
        
        # Clean up whitespace more efficiently
        plain_text = re.sub(r'\s+', ' ', plain_text).strip()
        
        return plain_text

    @staticmethod
    def extract_8k_document(text: str) -> Optional[str]:
        """
        Extracts the first <DOCUMENT>...</DOCUMENT> section that contains <TYPE>8-K.
        Optimized to avoid processing unnecessary documents.
        """
        if not text:
            return None
        
        # Find document sections more efficiently
        pattern = r"<DOCUMENT>(.*?)</DOCUMENT>"
        for match in re.finditer(pattern, text, re.DOTALL):
            doc_content = match.group(1)
            if "<TYPE>8-K" in doc_content:
                return TextProcessor.clean_html_text(doc_content)
        
        return None


class DateUtils:
    """Date utility functions."""
    
    @staticmethod
    def is_acceptance_date_valid(acceptance_date: str, lookback_days: int = Config.LOOKBACK_DAYS) -> bool:
        """
        Returns True if the acceptance_date is not older than lookback_days from today.
        Handles multiple date formats.
        """
        try:
            # Handle different date formats
            formats = [
                "%Y-%m-%dT%H:%M:%S.%fZ",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d"
            ]
            
            accepted_dt = None
            for fmt in formats:
                try:
                    accepted_dt = datetime.strptime(acceptance_date, fmt)
                    break
                except ValueError:
                    continue
            
            if accepted_dt is None:
                logger.warning(f"Unable to parse date: {acceptance_date}")
                return False
            
            # Ensure timezone awareness
            if accepted_dt.tzinfo is None:
                accepted_dt = accepted_dt.replace(tzinfo=timezone.utc)
            
            now = datetime.now(timezone.utc)
            return accepted_dt >= now - timedelta(days=lookback_days)
            
        except Exception as e:
            logger.warning(f"Error validating date {acceptance_date}: {e}")
            return False


class DatabaseManager:
    """Handles database operations with better error handling."""
    
    @staticmethod
    def save_to_db(df_new: pd.DataFrame, filename: str, merge_keys: List[str]) -> pd.DataFrame:
        """
        Saves new data to database, avoiding duplicates.
        Returns only the newly added rows.
        """
        if df_new.empty:
            logger.info(f"No new data to save to {filename}")
            return pd.DataFrame()
        
        filepath = Config.DB_DIR / f"{filename}.parquet"
        
        # Drop duplicates in df_new based on the merge keys
        df_new = df_new.drop_duplicates(subset=merge_keys)
        logger.debug(f"Processing {len(df_new)} potential new records")
        
        # If the file doesn't exist, save df_new and return all of it
        if not filepath.exists():
            df_new.to_parquet(filepath, index=False)
            logger.debug(f"Created new database file {filepath} with {len(df_new)} records")
            return df_new
        
        try:
            # Load existing data
            df_existing = pd.read_parquet(filepath)
            
            # Identify new rows (not in existing data)
            df_new_rows = df_new.merge(
                df_existing[merge_keys],
                on=merge_keys,
                how='left',
                indicator=True
            ).query("_merge == 'left_only'").drop(columns=["_merge"])
            
            # Append only if new rows exist
            if not df_new_rows.empty:
                df_updated = pd.concat([df_existing, df_new_rows], ignore_index=True)
                df_updated.to_parquet(filepath, index=False)
                logger.debug(f"Added {len(df_new_rows)} new records to {filepath}")
            else:
                logger.debug(f"No new records to add to {filepath}")
            
            return df_new_rows
            
        except Exception as e:
            logger.error(f"Error saving to database {filepath}: {e}")
            return pd.DataFrame()

    @staticmethod
    def save_result(result: Dict[str, Any]) -> None:
        """Save a single analysis result to database with auto-generated analysis_date."""
        if not result:
            return

        filepath = Config.DB_DIR / Config.RESULTS_DB_FILE

        try:
            # Add current timestamp as analysis_date
            analysis_date = datetime.now()
            serializable_result = result.copy()
            serializable_result['analysis_date'] = analysis_date

            # Convert CoinInfo objects to dictionaries for serialization
            if 'coins' in serializable_result and serializable_result['coins'] is not None:
                serializable_coins = {}
                for coin_name, coin_info in serializable_result['coins'].items():
                    if hasattr(coin_info, 'model_dump'):
                        serializable_coins[coin_name] = coin_info.model_dump()
                    elif hasattr(coin_info, '__dict__'):
                        serializable_coins[coin_name] = coin_info.__dict__
                    else:
                        serializable_coins[coin_name] = coin_info
                serializable_result['coins'] = serializable_coins

            df_result = pd.DataFrame([serializable_result])

            if filepath.exists():
                df_existing = pd.read_parquet(filepath)

                existing_mask = (
                    (df_existing['cik_str'] == result['cik_str']) &
                    (df_existing['ticker'] == result['ticker']) &
                    (df_existing['analysis_date'].dt.date == analysis_date.date())
                )

                if not existing_mask.any():
                    df_combined = pd.concat([df_existing, df_result], ignore_index=True)
                    df_combined.to_parquet(filepath, index=False)
                    logger.debug(f"Saved analysis result for {result['ticker']} to {filepath}")
                else:
                    logger.debug(f"Result for {result['ticker']} already exists for today")
            else:
                df_result.to_parquet(filepath, index=False)
                logger.debug(f"Created new results file {filepath} with result for {result['ticker']}")

        except Exception as e:
            logger.error(f"Error saving result for {result.get('ticker', 'unknown')}: {e}")

class FilingProcessor:
    max_filings_to_process = 10

    @classmethod
    def init(cls, max_filings_to_process: int = 10):
        cls.max_filings_to_process = max_filings_to_process
        logging.info(f"Max filings to process: {cls.max_filings_to_process}.")

    """Handles individual filing processing with better error handling."""
    
    @staticmethod
    def process_company_filings(cik_str: str, ticker: str, title: str) -> Optional[Dict[str, Any]]:
        """
        Process all recent 8-K filings for a single company.
        Returns analysis result if crypto-related material events found.
        """
        try:
            logger.debug(f"Processing filings for {ticker} ({cik_str})")
            
            # Get submission info
            submission = Edgar.get_submission_info((cik_str, ticker))
            if not submission or 'filings' not in submission:
                logger.warning(f"No submission data for {ticker}")
                return None
            
            filings = submission['filings']['recent']
            filings_df = pd.DataFrame(filings)
            
            if filings_df.empty:
                logger.debug(f"No recent filings for {ticker}")
                return None
            
            # Filter for valid 8-K filings
            valid_filings = filings_df[
                ((filings_df['form'] == '8-K') | (filings_df['form'] == '8-K/A')) &
                (filings_df['acceptanceDateTime'].apply(DateUtils.is_acceptance_date_valid))
            ]
            
            if valid_filings.empty:
                logger.debug(f"No valid 8-K or 8-K/A filings for {ticker}")
                return None
            
            # Process filings and concatenate content
            filing_content = ""
            processed_count = 0
            document_links = []
            for _, filing_row in valid_filings.iterrows():
                if processed_count >= FilingProcessor.max_filings_to_process:
                    break

                try:
                    raw_submission = EdgarRawData.retrieve_submission(
                        cik_str,
                        filing_row['accessionNumber'],
                        filing_row['primaryDocument']
                    )
                    
                    if raw_submission:
                        # Extract and clean 8-K content
                        clean_content = TextProcessor.clean_html_text(raw_submission)
                        if clean_content:
                            filing_content += f"\n--- Filing {filing_row['accessionNumber']} ---\n{clean_content}"
                            processed_count += 1
                            document_links.append(
                                EdgarRawData.retrieve_submission_document_link(
                                    cik_str,
                                    filing_row['accessionNumber'],
                                    filing_row['primaryDocument']
                                )
                            )
                    
                    # Add delay to avoid rate limiting
                    time.sleep(Config.REQUEST_DELAY)
                    
                except Exception as e:
                    logger.warning(f"Error processing filing {filing_row['accessionNumber']} for {ticker}: {e}")
                    continue
            
            if not filing_content.strip():
                logger.debug(f"No valid filing content for {ticker}")
                return None
            
            # Analyze content for crypto mentions
            try:
                evaluation = ai_8k_evaluator(filing_content)
                # print(f"Evaluation result for {ticker}: {evaluation}")
                if evaluation.contains_events and evaluation.recommendation == Recommendation.material_event:
                    logger.info(f"🚨 {ticker} - {title}: Cryptocurrencies disclosed as material event")
                    evaluation.cik_str = cik_str
                    evaluation.ticker = ticker
                    evaluation.title = title
                    html_links = [f'<a href="{url}">{url}</a>' for url in document_links]
                    html_output = "\n".join(html_links)
                    return {
                        "cik_str": cik_str,
                        "ticker": ticker,
                        "title": title,
                        "analysis_date": datetime.now(timezone.utc).isoformat(),
                        "filings_processed": processed_count,
                        "contains_crypto": bool(evaluation.crypto_coins),
                        "contains_non_crypto": bool(evaluation.non_crypto_events),
                        "crypto_mentions": evaluation.model_dump(mode='json')['crypto_coins'],
                        "non_crypto_mentions": evaluation.model_dump(mode='json')['non_crypto_events'],
                        "recommendation": evaluation.recommendation.value,
                        "summary": evaluation.notes,
                        "financial_summary": financial_summary(cik_str, ticker).get("summary", "N/A"),
                        "report_link": html_output,
                    }
                else:
                    logger.debug(f"No crypto material events found for {ticker}")
                    
            except Exception as e:
                logger.error(f"Error analyzing content for {ticker}: {e}")
                return None
            
        except Exception as e:
            logger.error(f"Error processing company {ticker} ({cik_str}): {e}")
            print(e.__traceback__.tb_frame.f_code.co_filename + ":" + str(e.__traceback__.tb_lineno) + " " + e.__traceback__.tb_frame.f_code.co_name)

            return None
        
        return None

class Notifier:
    """Handles sending notifications."""
    
    @staticmethod
    def send_to_telegram(message: str) -> None:
        if not Config.SEND_TO_TELEGRAM:
            logger.debug("Telegram notifications are disabled. Set SEND_TO_TELEGRAM to True to enable.")
            return

        """Send a notification via Telegram."""
        chat_ids = [*********]
        for chat_id in chat_ids:
            try:
                TelegramBot.send_text_via_telegram(
                    bot_token=TELEGRAM_BOT_TOKEN, 
                    chat_id=chat_id, 
                    text=message,
                )
            except Exception as e:
                logger.error(f"Error sending notification to chat ID {chat_id}: {e}")

class CryptoAnalyzer:
    """Main analyzer class with sequential processing."""
    def __init__(self) -> None:
        args = parse_arguments()
        self.parse_all = args.all
        if self.parse_all:
            logging.info("--all passed: parsing all the stocks.")
        else:
            logging.info("--all not passed: parsing only recent filings.")
        
        FilingProcessor.init(args.max_filings_to_process)
    
    def run_analysis_cycle(self) -> None:
        """Run a single analysis cycle."""
        try:
            logger.debug("Starting analysis cycle...")
            
            # Get recent submissions
            if self.parse_all:
                logger.debug("Parsing all companies")
                stocks_df = Edgar.get_all_stocks_to_dataframe()
                stocks_df['acceptanceDateTime'] = datetime.now(timezone.utc)
                stocks_df = stocks_df.sample(frac=1).reset_index(drop=True)
            else:
                logger.debug("Parsing recent submissions")
                stocks_df = Edgar.get_all_recent_submissions()
                
            if stocks_df.empty:
                logger.debug("No recent submissions found")
                return

            # Save to database and get only new records
            new_stocks = DatabaseManager.save_to_db(
                stocks_df, 
                Config.STOCKS_DB_FILE.replace('.parquet', ''),
                ["cik_str", "acceptanceDateTime"]
            )
            
            if new_stocks.empty:
                logger.debug("No new stocks to process")
                return
            
            # Convert to list of tuples for processing
            companies = [
                (row.cik_str, row.ticker, row.title)
                for row in new_stocks.itertuples(index=False)
            ]
            
            logger.debug(f"Processing {len(companies)} companies sequentially for crypto analysis")
            
            # Process companies one by one with progress bar
            now = datetime.now(ZoneInfo("Europe/Berlin"))
            descr = f"{now.strftime('%d/%m/%Y %H:%M:%S')}"
            crypto_findings = 0
            for cik_str, ticker, title in tqdm(companies, desc=descr, colour='green'):
                # if ticker not in ["SBET", "BLNE", "MSTR", "COIN", "RIOT", "HUT", "UFPI", "SOFI", "PFSA", "BKKT"]:
                #     print(f"Skipping {ticker}...")
                #     continue
                # print(f"Analyzing content for {ticker}...")
                try:
                    result = FilingProcessor.process_company_filings(cik_str, ticker, title)
                    
                    if result:
                        DatabaseManager.save_result(result)
                        crypto_findings += 1
                        
                        # "contains_crypto": bool(evaluation.crypto_coins),
                        # "contains_non_crypto": bool(evaluation.non_crypto_events),
                        # "crypto_mentions": evaluation.model_dump(mode='json')['crypto_coins'],
                        # "non_crypto_mentions": evaluation.model_dump(mode='json')['non_crypto_events'],
                        lines = []
                        if result['contains_crypto']:
                            lines.append(result['crypto_mentions'])
                        if result['contains_non_crypto']:
                            lines.append(result['non_crypto_mentions'])

                        events_info = "\n".join(lines) + "\n" if lines else ""
                        # Print immediate result
                        output_str = f"🎯 FINDINGS: {ticker} - {title}\n" \
                                    f"   CIK: {cik_str}\n" \
                                    f"   Filings processed: {result['filings_processed']}\n" \
                                    f"   Summary: {result['summary']}\n" \
                                    f"   Financial summary: {result['financial_summary']}\n" \
                                    f"   Events: {events_info}" \
                                    f"   Report links:{result['report_link']}\n"
                        print(output_str)
                        print("-" * 80)
                        Notifier.send_to_telegram(output_str)
                    
                    # Small delay between companies to be respectful to the API
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"Error processing company {ticker}: {e}")
                    continue
            
            if crypto_findings > 0:
                logger.debug(f"Analysis complete! Found {crypto_findings} companies with crypto-related material events")
            else:
                logger.debug("Analysis complete! No crypto-related material events found")
                
        except Exception as e:
            logger.error(f"Error in analysis cycle: {e}")
    
    def run_continuous(self) -> None:
        """Run continuous analysis with proper error handling."""
        logger.debug("Starting continuous 8-K crypto analysis...")
        logger.debug(f"Configuration: Lookback={Config.LOOKBACK_DAYS} days, Sleep={Config.SLEEP_INTERVAL}s")
        
        cycle_count = 0
        while True:
            try:
                cycle_count += 1
                logger.debug(f"Starting analysis cycle #{cycle_count}")
                
                start_time = time.time()
                self.run_analysis_cycle()
                end_time = time.time()
                
                logger.debug(f"Cycle #{cycle_count} completed in {end_time - start_time:.2f} seconds")
                logger.debug(f"Sleeping for {Config.SLEEP_INTERVAL} seconds...")
                time.sleep(Config.SLEEP_INTERVAL)
                
            except KeyboardInterrupt:
                logger.debug("Analysis stopped by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error in cycle #{cycle_count}: {e}")
                logger.debug(f"Retrying in {Config.SLEEP_INTERVAL} seconds...")
                time.sleep(Config.SLEEP_INTERVAL)


def main():
    """Main entry point."""
    try:
        analyzer = CryptoAnalyzer()
        analyzer.run_continuous()
    except Exception as e:
        logger.error(f"Fatal error in main: {e}")
        raise


if __name__ == '__main__':
    main()