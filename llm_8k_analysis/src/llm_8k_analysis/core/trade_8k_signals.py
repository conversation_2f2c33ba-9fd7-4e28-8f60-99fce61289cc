from lumibot.brokers import Alpaca
from lumibot.entities import Asset, Order
from lumibot.strategies.strategy import Strategy
from lumibot.traders import Trader

from monitor import filter_recent_entries, process_company
from config import TELEGRAM_BOT_TOKEN, ALPACA_API_KEY, ALPACA_API_SECRET, ALPACA_PAPER_TRADING

from datetime import datetime
from zoneinfo import ZoneInfo
import time
from tqdm import tqdm
from utils.telegram_bot import TelegramBot

# Alpaca configuration from environment variables
ALPACA_CONFIG = {
    "API_KEY": ALPACA_API_KEY,
    "API_SECRET": ALPACA_API_SECRET,
    "PAPER": ALPACA_PAPER_TRADING
}

class Trade8KSignals(Strategy):
    # Custom parameters
    parameters = {
        "symbol": "SPY",
        "quantity": 200,
        "side": "buy"
    }

    def initialize(self, symbol=""):
        # Will make on_trading_iteration() run every 30 minutes
        self.sleeptime = 30
        self.local_db = {}
        self.portfolio = {}
        self.set_market("NASDAQ")

    def on_trading_iteration(self):
        if self.first_iteration:
            self.local_db = {}
        
        df_signals = filter_recent_entries(
                filename="db/general_results.parquet", 
                max_days=30
            )

        # Convert to list of tuples for processing
        companies = [
            (row.cik_str, row.ticker, row.title, row.summary, row.analysis_date, row.report_link)
            for row in df_signals.itertuples(index=False)
        ]
        # Process companies one by one with progress bar
        now = datetime.now(ZoneInfo("Europe/Berlin"))
        descr = f"{now.strftime('%d/%m/%Y %H:%M:%S')}"
        for cik_str, symbol, title, summary, analysis_date, report_link in tqdm(companies, desc=descr, colour='blue'):
            tic = time.time()
            self.local_db = process_company(cik_str, symbol, title, summary, analysis_date, report_link, self.local_db)

            if symbol in self.portfolio or symbol not in self.local_db:
                self.logger.info(f"{symbol} already in portfolio, skipping.")
                continue
            
            # print(self.local_db[symbol])
            if self.local_db[symbol]['times_signaled'] > 2: # Signaled more than 2 times
                self.logger.info(f"{symbol} has been signaled {self.local_db[symbol]['times_signaled']} times.")
                price = self.get_last_price(symbol)
                if price is None:
                    self.logger.warning(f"Could not retrieve price for {symbol}. Skipping order.")
                    continue

                quantity = self.parameters['quantity'] // price  # Calculate quantity based on $200 budget
                order = self.create_order(symbol, quantity, "buy")
                self.logger.info(f"Placing order for {symbol}: {quantity} shares at {price} each.")
                self.submit_order(order)
                self.wait_for_order_execution(order)
                self.logger.info(f"Order executed for {symbol}: {quantity} shares at {price} each.")

                self.portfolio[symbol] = {
                    'quantity': quantity,
                    'price': price,
                    'times_signaled': self.local_db[symbol]['times_signaled'],
                    'date': self.local_db[symbol]['date'],
                    'trail_order': {
                        'asset': symbol,
                        'quantity': quantity,
                        'side': Order.OrderSide.SELL,
                        'trail_percent': 3.0, # 0-100 percent trailing stop,
                        'time_in_force': "gtc"
                    }
                }

                trailing_stop_order = self.create_order(
                    asset=symbol, 
                    quantity=self.portfolio[symbol]['trail_order']['quantity'], 
                    side=self.portfolio[symbol]['trail_order']['side'],
                    trail_percent=self.portfolio[symbol]['trail_order']['trail_percent'],
                    time_in_force=self.portfolio[symbol]['trail_order']['time_in_force'],
                )
                self.submit_order(trailing_stop_order)
                self.logger.info(f"Placed trailing stop order for {symbol} with trail percent {self.portfolio[symbol]['trail_order']['trail_percent']}%.")
                TelegramBot.send_text_via_telegram(
                    bot_token=TELEGRAM_BOT_TOKEN,
                    chat_id=383304820,
                    text=f"{symbol} has been bought {self.local_db[symbol]['times_signaled']} times."
                )
            toc = time.time()
            elapsed_time = toc - tic
            available_sleeptime_in_seconds = (0.9 * 60 * self.sleeptime)
            wait_time = available_sleeptime_in_seconds / len(companies if companies else 1) - elapsed_time
            self.sleep(max(1, wait_time))

    def on_abrupt_closing(self):
        self.logger.warning("Abrupt closing detected. ")
        exit()

if __name__ == "__main__":
    broker = Alpaca(ALPACA_CONFIG)
    strategy = Trade8KSignals(name="Trade8KSignals", budget=5000, broker=broker)
    trader = Trader(strategies=[strategy], logfile="logs/trade8k.log")
    trader.run_all()
