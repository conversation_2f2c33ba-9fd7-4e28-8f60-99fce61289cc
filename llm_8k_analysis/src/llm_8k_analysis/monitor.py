import pandas as pd
import yfinance as yf
import logging
import time
from datetime import datetime
from zoneinfo import ZoneInfo
from utils.telegram_bot import TelegramBot
from config import TELEGRAM_BOT_TOKEN
from utils.stock_chart import create_stock_chart
from utils.stock_data import financial_summary
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_monitor_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

CYCLE_DURATION =   30 * 60 * 1  # 1 hour in seconds
Z_SCORE_THRESHOLD = 2.0
TESTING = True

def signal_stock(cik_str:str, ticker: str, title:str, summary: str, analysis_date: str, report_link: str):
    filename = create_stock_chart(ticker, period='1mo', save_dir='charts')
    financial_info = financial_summary(cik_str, ticker).get("summary", "N/A")

    """Send a notification via Telegram."""
    chat_ids = TelegramBot.get_list_of_clients(TELEGRAM_BOT_TOKEN)
    if TESTING:
        chat_ids = chat_ids[:1]  # Limit to one chat ID for testing

    for (chat_id, name) in chat_ids:
        message = f"Hi {name}! Found: {ticker} - {title}" \
            f"\nCIK: {cik_str}" \
            f"\nSummary: {summary}" \
            f"\nFinancial summary: {financial_info}" \
            f"\nReport links: {report_link}" \
            f"\nTradingView news: <a href='https://www.tradingview.com/symbols/{ticker}/news'>TradingView News</a>" \
            f"\nYahoo finance: <a href='https://finance.yahoo.com/quote/{ticker}'>Yahoo Finance</a>" \
            f"\nTradingView chart: <a href='https://www.tradingview.com/chart/?symbol={ticker}'>TradingView Chart</a>" \
            f"\nAnalysis date: {analysis_date}"
        logger.info(f"Sending notification to chat ID {chat_id}: {message}. Filename: {filename}")
        try:
            TelegramBot.send_image_via_telegram(
                bot_token=TELEGRAM_BOT_TOKEN, 
                chat_id=chat_id, 
                image_path=filename,
                caption=f"Price movement {title}! "
            )
            TelegramBot.send_text_via_telegram(
                bot_token=TELEGRAM_BOT_TOKEN,
                chat_id=chat_id,
                text=message
            )
        except Exception as e:
            logger.error(f"Error sending notification to chat ID {chat_id}: {e}")



def filter_recent_entries(filename="db/general_results.parquet", max_days=30):
    """
    Filters the DataFrame to include only entries with 'analysis_date' within the last max_days.
    """
    # Load the data
    df = pd.read_parquet(filename, engine="pyarrow")
    # Display the first few rows
    # remove dublicates, keep the most recent entry based on 'analysis_date'
    df = df.sort_values(by='analysis_date').drop_duplicates(subset=['cik_str'], keep='last')
    df = df.dropna(subset=['cik_str', 'ticker', 'analysis_date', 'report_link'])

    now = pd.Timestamp.now()
    cutoff_date = now - pd.Timedelta(days=max_days)
    df = df[df['analysis_date'] >= cutoff_date]
    # sort by 'analysis_date' by most recent first
    df = df.sort_values(by='analysis_date', ascending=False)
    return df.reset_index(drop=True)

def process_company(cik_str, ticker, title, summary, analysis_date, report_link, local_db):
    """
    Mini cycle to process a single stock entry.
    """
    data = yf.Ticker(ticker)
    history = data.history(period='1mo')
    history['PctChange'] = history['Close'].pct_change() * 100
    history = history.dropna()  # Drop NA values after pct_change

    mean_change = history['PctChange'].mean()
    std_change = history['PctChange'].std()
    latest_change = history['PctChange'].iloc[-1]
    z_score = (latest_change - mean_change) / std_change

    current_date = history.index[-1].strftime('%Y-%m-%d %H:%M:%S')
    previous_date = local_db.get(ticker, {}).get('date')
    # If ticker not in db or it's a new day → reset entry
    if previous_date != current_date:
        logging.info(f"{ticker}: New day detected or new entry, initializing local_db entry.")
        local_db[ticker] = {
            'mean_change': mean_change,
            'std_change': std_change,
            'latest_change': latest_change,
            'z_score': z_score,
            'times_signaled': 0,
            'date': current_date
        }

    last_z_score = local_db[ticker]['z_score']
    local_db[ticker]['z_score'] = z_score
    local_db[ticker]['latest_change'] = latest_change
    local_db[ticker]['mean_change'] = mean_change
    local_db[ticker]['std_change'] = std_change

    if (z_score > max(Z_SCORE_THRESHOLD, last_z_score)):
        logging.info(f"{ticker}: 🚀 Price is increasing unusually fast!")
        local_db[ticker]['times_signaled'] += 1
        signal_stock(
            cik_str=cik_str, 
            ticker=ticker,
            title=title,
            summary=summary, 
            analysis_date=analysis_date, 
            report_link=report_link
        )
    elif z_score < -Z_SCORE_THRESHOLD and z_score < last_z_score:
        logging.info(f"{ticker}: 📉 Price is dropping unusually fast!")
    else:
        logging.debug(f"{ticker}: Price movement is within normal range.")
    
    return local_db
        
def cycle(df, local_db: dict, cycle_duration=CYCLE_DURATION):
    # Convert to list of tuples for processing
    companies = [
        (row.cik_str, row.ticker, row.title, row.summary, row.analysis_date, row.report_link)
        for row in df.itertuples(index=False)
    ]
    # Process companies one by one with progress bar
    now = datetime.now(ZoneInfo("Europe/Berlin"))
    descr = f"{now.strftime('%d/%m/%Y %H:%M:%S')}"

    for cik_str, ticker, title, summary, analysis_date, report_link in tqdm(companies, desc=descr, colour='blue'):
        tic = time.time()
        local_db = process_company(cik_str, ticker, title, summary, analysis_date, report_link, local_db)
        # Sleep to maintain cycle duration
        toc = time.time()
        elapsed_time = toc - tic
        wait_time = cycle_duration / len(companies if companies else 1) - elapsed_time
        time.sleep(max(1, wait_time))
    return local_db

    
if __name__ == "__main__":
    local_db = {}

    while True:
        try:
            df_signals = filter_recent_entries(
                filename="db/general_results.parquet", 
                max_days=30
            )
            local_db = cycle(df_signals, local_db)
        except Exception as e:
            print(e.__traceback__.tb_frame.f_code.co_filename + ":" + str(e.__traceback__.tb_lineno) + " " + e.__traceback__.tb_frame.f_code.co_name)