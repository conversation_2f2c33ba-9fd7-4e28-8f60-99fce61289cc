# stock split API https://financialmodelingprep.com/api/v3/historical-price-full/stock_split/nvda?apikey=bStjIKcA8PCbN4PSAYZ3Mn2K7GwjORQy
# edgar filling
import re
import requests
import json
import logging
import os
import pandas as pd
import datetime
import random
import time
from config import *
from dateutil import parser
from bs4 import BeautifulSoup

# create request header
headers = {'User-Agent': "<NAME_EMAIL>"}
# headers = {
#     'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36', 
#     'Accept-Encoding': 'gzip, deflate'
# }


# Counter({'dei': 8711, 'us-gaap': 8305, 'invest': 1271, 'srt': 1134, 'ifrs-full': 1060})
# https://github.com/ekmungai/python-accounting/blob/main/python_accounting/reports/income_statement.py
# https://github.com/ekmungai/python-accounting/blob/main/config.toml
# https://github.com/ekmungai/python-accounting/blob/main/python_accounting/config.py#L149
#'https://data.sec.gov/api/xbrl/companyfacts/CIK{:>010s}.json'.format(cik)
# http://www.xbrlsite.com/2015/Library/UnderstandingTheMechanicsOfDigitalFinancialReport.pdf


class EdgarRawData:
    @staticmethod
    def force_update_company_facts(cik:str, ticker: str) -> dict:
        # forced update
        # create folder if not exists
        folder_name = EDGAR_RAW_DATA_FOLDER
        file_path = EDGAR_RAW_DATA_FILE_PATH.format(ticker, cik)

        try:
            if not os.path.exists(folder_name):
                os.makedirs(folder_name)
        except Exception as e:
            logging.warning(f'Error creating folder {folder_name}, {e}')
            return None
        
        try:
            # wait random time to avoid rate limit
            # initialize random seed with current time
            response = requests.get(EDGAR_COMPANY_FACTS_URL.format(cik), headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
            response.raise_for_status()
            with open(file_path, 'w') as file:
                json.dump(response.json(), file)

            return response.json()    
        except Exception as e:
            logging.warning(f'Error updating company facts for {ticker}, {e}, type: {type(e)}')
            return None
    

    @staticmethod
    def get_raw_company_tickers() -> pd.DataFrame:
        # get all companies data
        response = requests.get(EDGAR_COMPANY_TICKERS_URL, headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
        response.raise_for_status()

        return response.json()
    
    @staticmethod
    def get_raw_submissions(cik: str) -> dict:
        # get company fillings
        response = requests.get(EDGAR_COMPANY_SUBMISSIONS_URL.format(cik), headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
        response.raise_for_status()
        return response.json()

    @staticmethod
    def retrieve_submission(cik: str, accession_number: str) -> dict:
        url = 'https://www.sec.gov/Archives/edgar/data/{}/{}.txt'
        response = requests.get(url.format(cik, accession_number), headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
        response.raise_for_status()
        return response.text

    @staticmethod
    def retrieve_submission_document_link(cik: str, accession_number: str, document_id: str) -> str:
        """
        Retrieve the document link for a specific filing.
        
        Args:
            cik (str): The Central Index Key of the company.
            accession_number (str): The accession number of the filing.
            document_id (str): The document ID to retrieve.
        
        Returns:
            str: The URL to the document.
        """
        url = 'https://www.sec.gov/Archives/edgar/data/{}/{}/{}'
        accession_number_no_dash = accession_number.replace('-', '')
        return url.format(cik, accession_number_no_dash, document_id)
    
    @staticmethod
    def retrieve_submission(cik: str, accession_number: str, document_id: str) -> dict:
        response = requests.get(EdgarRawData.retrieve_submission_document_link(cik, accession_number, document_id), headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
        response.raise_for_status()
        return response.text


class Edgar:    
    @staticmethod
    def get_all_stocks_to_dataframe() -> pd.DataFrame:
        response = EdgarRawData.get_raw_company_tickers()
        companyData = pd.DataFrame.from_dict(response, orient='index')
        # add leading zeros to CIK
        companyData['cik_str'] = companyData['cik_str'].astype(str).str.zfill(10)
        return companyData    

    
    @staticmethod
    def get_submission_info(cik_tiker: tuple[str, str]) -> dict:
        try:
            cik = cik_tiker[0]
            response = EdgarRawData.get_raw_submissions(cik)
            return response
        except requests.exceptions.HTTPError as e:
            random_sleep = random.randint(1, 10)
            time.sleep(random_sleep)
            logging.error(f'Random sleep for: {cik}, {e}, type: {type(e)}')
            # return Edgar.get_submission_info(cik_tiker)
            return None
        except Exception as e:
            logging.error(f'Error getting submission info for CIK: {cik}, {e}, type: {type(e)}')
            return None
    
    @staticmethod
    def get_all_recent_submissions() -> pd.DataFrame:
        try:
            accepted_form_types = ['8-K', '8-K/A']
            companies = Edgar.get_all_stocks_to_dataframe()
            companies["new_publication"] = False
            for supported_form_type in accepted_form_types:
                response = requests.get(EDGAR_FILING_DETAILS_URL.format(supported_form_type), headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
                soup = BeautifulSoup(response.text, features="xml")
                for entry in soup.find_all('entry'):
                    form_type = entry.category.get('term')
                    # category_label = entry.category.get('label')
                    if form_type != supported_form_type:# or category_label != 'form type':
                        continue

                    cik_str = re.search(r'\((\d{10})\)', entry.title.text).group(1)
                    acceptance_datetime: datetime = parser.parse(entry.updated.text)
                    companies.loc[companies['cik_str'] == cik_str, "new_publication"] = True
                    companies.loc[companies['cik_str'] == cik_str, "acceptanceDateTime"] = acceptance_datetime

                
                companies = companies[companies["new_publication"] == True]
                companies.drop(columns=['new_publication'], inplace=True)
                companies.reset_index(drop=True, inplace=True)
                return companies
        except requests.exceptions.ConnectTimeout as e:
            logging.warning(f'Timed out connect to SEC servver. {e}')
        except Exception as e:
            logging.error(f'Error getting latest submissions. Exception type: {type(e)}')

        return None

        
    
if __name__ == '__main__':
    start_time = datetime.now()
    df = Edgar.get_all_recent_submissions()
    end_time = datetime.now()
    if df is not None:
        print(df.to_string())
    print(f'Time elapsed: {end_time - start_time}')
