import asyncio
import telegram

class TelegramBot:
    @staticmethod
    async def get_list_of_clients_async(bot_token: str) -> telegram.Update:
        bot = telegram.Bot(token=bot_token)
        return await bot.get_updates()

    @staticmethod
    def get_list_of_clients(bot_token: str) -> set[int]:
        # loop = asyncio.get_event_loop()
        # updates = loop.run_until_complete(TelegramBot.get_list_of_clients_async(bot_token=bot_token))
        # return {(383304820, "RealBlackCelsius")}
        return [(383304820, "RealBlackCelsius"), (220823605, "PUA MUFI"), (316216601, "Ciucciolo")]
        # return {(update.message.chat.id, update.message.chat.username) for update in updates}

    @staticmethod
    def send_image_via_telegram(bot_token, chat_id, image_path, caption=None):
        # Initialize the bot
        bot = telegram.Bot(token=bot_token)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        async def send_image():
            with open(image_path, 'rb') as image_file:
                await bot.send_photo(chat_id=chat_id, photo=image_file, caption=caption, parse_mode='HTML', 
                                     show_caption_above_media=True)

        loop.run_until_complete(send_image())

    @staticmethod
    def send_text_via_telegram(bot_token, chat_id, text):
        # Initialize the bot
        bot = telegram.Bot(token=bot_token)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        async def send_message():
            await bot.send_message(chat_id=chat_id, text=text, parse_mode='HTML')

        loop.run_until_complete(send_message())

if __name__ == "__main__":
    TelegramBot.send_text_via_telegram(bot_token="**********************************************", chat_id=383304820, text="Hello, World!")
