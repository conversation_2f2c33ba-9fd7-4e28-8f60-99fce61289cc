import requests
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import json

class StockData:
    
    @staticmethod
    def get_market_data(cik_ticker: str, ticker: str) -> Optional[Dict[str, Any]]:
        """
        Return market data, including 
        Company Profile by CIK API
        Retrieve detailed company profile data by CIK (Central Index Key) with the FMP Company Profile by CIK API.
        This API allows users to search for companies using their unique CIK identifier and access a full range of company data, including stock price, market capitalization, industry, and much more.
        https://financialmodelingprep.com/stable/profile-cik?cik={}&apikey={FINANCIAL_MODELING_PREP_API_KEY}

        Example output:
        [
            {
                "symbol": "AAPL",
                "price": 232.8,
                "marketCap": 3500823120000,
                "beta": 1.24,
                "lastDividend": 0.99,
                "range": "164.08-260.1", <-- 52 week range
                "change": 4.79, <-- today's change
                "changePercentage": 2.1008,
                "volume": 0,
                "averageVolume": 50542058,
                "companyName": "Apple Inc.",
                "currency": "USD",
                "cik": "0000320193",
                "isin": "US*********5",
                "cusip": "*********",
                "exchangeFullName": "NASDAQ Global Select",
                "exchange": "NASDAQ",
                "industry": "Consumer Electronics",
                "website": "https://www.apple.com",
                "description": "Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide. The company offers iPhone, a line of smartphones; Mac, a line of personal computers; iPad, a line of multi-purpose tablets; and wearables, home, and accessories comprising AirPods, Apple TV, Apple Watch, Beats products, and HomePod. It also provides AppleCare support and cloud services; and operates various platforms, including the App Store that allow customers to discov...",
                "ceo": "Mr. Timothy D. Cook",
                "sector": "Technology",
                "country": "US",
                "fullTimeEmployees": "164000",
                "phone": "(*************",
                "address": "One Apple Park Way",
                "city": "Cupertino",
                "state": "CA",
                "zip": "95014",
                "image": "https://images.financialmodelingprep.com/symbol/AAPL.png",
                "ipoDate": "1980-12-12",
                "defaultImage": false,
                "isEtf": false,
                "isActivelyTrading": true,
                "isAdr": false,
                "isFund": false
            }
        ]
        """
        # Import here to avoid circular import issues
        try:
            from config import FINANCIAL_MODELING_PREP_API_KEY
            api_key = FINANCIAL_MODELING_PREP_API_KEY
        except ImportError:
            import os
            api_key = os.getenv('FINANCIAL_MODELING_PREP_API_KEY')
        
        if not api_key:
            print("Error: Set FINANCIAL_MODELING_PREP_API_KEY in config.py or as environment variable.")
            return {}
        
        try:
            url = f"https://financialmodelingprep.com/stable/profile-cik?cik={cik_ticker}&apikey={FINANCIAL_MODELING_PREP_API_KEY}"
            response = requests.get(url)
            response.raise_for_status()
            
            data = response.json()
            return data[0] if data else {}
            
        except Exception as e:
            print(f"Error fetching market data: {e}")
            return {}

class StockAnalyzer:
    def __init__(self, api_key: str):
        self.api_key = api_key
    
    def get_financial_ratios(self, ticker: str) -> Optional[Dict[str, Any]]:
        """
        Get key financial ratios for the company
        """
        try:
            base_url = "https://financialmodelingprep.com"
            url = f"{base_url}/api/v3/ratios/{ticker}"
            params = {"apikey": self.api_key, "limit": 1}
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            return data[0] if data else None
            
        except Exception as e:
            print(f"Error fetching financial ratios: {e}")
            return None
    
    def get_income_statement(self, ticker: str) -> Optional[Dict[str, Any]]:
        """
        Get latest income statement data
        """
        try:
            base_url = "https://financialmodelingprep.com"
            url = f"{base_url}/api/v3/income-statement/{ticker}"
            params = {"apikey": self.api_key, "limit": 1}
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            return data[0] if data else None
            
        except Exception as e:
            print(f"Error fetching income statement: {e}")
            return None
    
    def analyze_valuation(self, market_data: Dict[str, Any], ratios: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze company valuation metrics
        """
        analysis = {
            "market_cap": market_data.get("marketCap", 0),
            "price": market_data.get("price", 0),
            "pe_ratio": ratios.get("priceEarningsRatio", 0) if ratios else 0,
            "pb_ratio": ratios.get("priceToBookRatio", 0) if ratios else 0,
            "price_to_sales": ratios.get("priceToSalesRatio", 0) if ratios else 0,
            "valuation_assessment": self._assess_valuation(ratios if ratios else {})
        }
        return analysis
    
    def analyze_financial_health(self, ratios: Dict[str, Any], income_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze company's financial health
        """
        if not ratios or not income_data:
            return {"status": "insufficient_data", "financial_strength": "unknown"}
        
        analysis = {
            "current_ratio": ratios.get("currentRatio", 0),
            "debt_to_equity": ratios.get("debtEquityRatio", 0),
            "return_on_equity": ratios.get("returnOnEquity", 0),
            "return_on_assets": ratios.get("returnOnAssets", 0),
            "gross_profit_margin": ratios.get("grossProfitMargin", 0),
            "net_profit_margin": ratios.get("netProfitMargin", 0),
            "revenue": income_data.get("revenue", 0),
            "net_income": income_data.get("netIncome", 0),
            "financial_strength": self._assess_financial_strength(ratios)
        }
        return analysis
    
    def analyze_market_performance(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze market performance metrics
        """
        analysis = {
            "current_price": market_data.get("price", 0),
            "day_change": market_data.get("change", 0),
            "day_change_percent": market_data.get("changePercentage", 0),
            "52_week_range": market_data.get("range", "N/A"),
            "volume": market_data.get("volume", 0),
            "average_volume": market_data.get("averageVolume", 0),
            "beta": market_data.get("beta", 0),
            "dividend_yield": self._calculate_dividend_yield(market_data),
            "volatility_assessment": self._assess_volatility(market_data)
        }
        return analysis
    
    def _assess_valuation(self, ratios: Dict[str, Any]) -> str:
        """
        Assess if stock appears overvalued, undervalued, or fairly valued
        """
        pe_ratio = ratios.get("priceEarningsRatio", 0)
        pb_ratio = ratios.get("priceToBookRatio", 0)
        
        if pe_ratio == 0 or pb_ratio == 0:
            return "insufficient_data"
        
        # Simple heuristic-based assessment
        if pe_ratio > 25 and pb_ratio > 3:
            return "potentially_overvalued"
        elif pe_ratio < 15 and pb_ratio < 1.5:
            return "potentially_undervalued"
        else:
            return "fairly_valued"
    
    def _assess_financial_strength(self, ratios: Dict[str, Any]) -> str:
        """
        Assess overall financial strength
        """
        current_ratio = ratios.get("currentRatio", 0)
        debt_to_equity = ratios.get("debtEquityRatio", 0)
        roe = ratios.get("returnOnEquity", 0)
        
        strong_indicators = 0
        
        if current_ratio > 1.5:
            strong_indicators += 1
        if debt_to_equity < 0.5:
            strong_indicators += 1
        if roe > 0.15:
            strong_indicators += 1
        
        if strong_indicators >= 2:
            return "strong"
        elif strong_indicators == 1:
            return "moderate"
        else:
            return "weak"
    
    def _assess_volatility(self, market_data: Dict[str, Any]) -> str:
        """
        Assess stock volatility based on beta
        """
        beta = market_data.get("beta", 1)
        
        if beta > 1.3:
            return "high_volatility"
        elif beta < 0.7:
            return "low_volatility"
        else:
            return "moderate_volatility"
    
    def _calculate_dividend_yield(self, market_data: Dict[str, Any]) -> float:
        """
        Calculate dividend yield
        """
        dividend = market_data.get("lastDividend", 0)
        price = market_data.get("price", 0)
        
        if price > 0 and dividend > 0:
            # Annual dividend yield (assuming quarterly dividend)
            return (dividend * 4 / price) * 100
        return 0
    
    def generate_comprehensive_analysis(self, cik_ticker: str, ticker: str) -> Dict[str, Any]:
        """
        Generate comprehensive stock analysis
        """
        # Fetch all required data
        market_data = StockData.get_market_data(cik_ticker, ticker)
        ratios = self.get_financial_ratios(ticker)
        income_data = self.get_income_statement(ticker)
        
        if not market_data:
            return {"error": "Unable to fetch market data", "ticker": ticker}
        
        # Perform analyses
        valuation_analysis = self.analyze_valuation(market_data, ratios)
        financial_health = self.analyze_financial_health(ratios, income_data)
        market_performance = self.analyze_market_performance(market_data)
        
        # Company information
        company_info = {
            "symbol": market_data.get("symbol", ticker),
            "company_name": market_data.get("companyName", "N/A"),
            "sector": market_data.get("sector", "N/A"),
            "industry": market_data.get("industry", "N/A"),
            "ceo": market_data.get("ceo", "N/A"),
            "employees": market_data.get("fullTimeEmployees", "N/A"),
            "headquarters": f"{market_data.get('city', 'N/A')}, {market_data.get('state', 'N/A')}",
            "website": market_data.get("website", "N/A"),
            "ipo_date": market_data.get("ipoDate", "N/A"),
            "exchange": market_data.get("exchange", "N/A")
        }
        
        # Generate summary
        summary = self._generate_summary(
            company_info, valuation_analysis, financial_health, market_performance
        )
        
        # Compile comprehensive analysis
        analysis = {
            "timestamp": datetime.now().isoformat(),
            "ticker": ticker,
            "company_info": company_info,
            "valuation_analysis": valuation_analysis,
            "financial_health": financial_health,
            "market_performance": market_performance,
            "summary": summary
        }
        
        return analysis
    
    def _generate_summary(self, company_info: Dict, valuation: Dict, 
                         financial_health: Dict, market_perf: Dict) -> str:
        """
        Generate executive summary of the analysis
        """
        company_name = company_info.get("company_name", "the company")
        sector = company_info.get("sector", "N/A")
        
        # Market performance summary
        price = market_perf.get("current_price", 0)
        change_pct = market_perf.get("day_change_percent", 0)
        change_direction = "up" if change_pct > 0 else "down" if change_pct < 0 else "flat"
        
        # Valuation summary
        market_cap = valuation.get("market_cap", 0)
        market_cap_formatted = f"${market_cap/1e9:.1f}B" if market_cap > 1e9 else f"${market_cap/1e6:.1f}M"
        valuation_assessment = valuation.get("valuation_assessment", "unknown")
        
        # Financial health summary
        financial_strength = financial_health.get("financial_strength", "unknown")
        
        summary = f"""
{company_name} is a {sector} sector company currently trading at ${price:.2f}, {change_direction} {abs(change_pct):.2f}% today. 
With a market capitalization of {market_cap_formatted}, the stock appears {valuation_assessment.replace('_', ' ')} 
based on current valuation metrics.

Financial Health: The company demonstrates {financial_strength} financial health based on key ratios including 
liquidity, profitability, and leverage metrics.

Market Position: Trading with {market_perf.get('volatility_assessment', 'moderate').replace('_', ' ')}, 
the stock shows a beta of {market_perf.get('beta', 'N/A')}, indicating 
{'higher than market' if market_perf.get('beta', 1) > 1.1 else 'lower than market' if market_perf.get('beta', 1) < 0.9 else 'similar to market'} 
volatility.

Investment Considerations: 
- Valuation appears {valuation_assessment.replace('_', ' ')}
- Financial strength is {financial_strength}
- Market volatility is {market_perf.get('volatility_assessment', 'moderate').replace('_', ' ')}
- Dividend yield: {market_perf.get('dividend_yield', 0):.2f}%

This analysis is based on the most recent available financial data and market conditions.
        """.strip()
        
        return summary

# Example usage demonstrating static method call
def main():
    """
    Example usage of the comprehensive stock analysis system
    """
    # Import API key
    try:
        from config import FINANCIAL_MODELING_PREP_API_KEY
    except ImportError:
        import os
        FINANCIAL_MODELING_PREP_API_KEY = os.getenv('FINANCIAL_MODELING_PREP_API_KEY')
        if not FINANCIAL_MODELING_PREP_API_KEY:
            print("Error: No API key found. Set FINANCIAL_MODELING_PREP_API_KEY in config.py or as environment variable.")
            return
    
    # Now you can call the static method directly without any instantiation
    ticker = "AAPL"
    cik_ticker = "0000320193"
    
    print("Calling static method StockData.get_market_data()...")
    market_data = StockData.get_market_data(cik_ticker, ticker)
    
    print("\nMarket Data:", market_data)
    if market_data:
        print(f"Successfully retrieved data for {market_data.get('companyName', ticker)}")
        print(f"Current price: ${market_data.get('price', 'N/A')}")
        # Fix the formatting error - handle both numeric and string values
        market_cap = market_data.get('marketCap', 'N/A')
        if isinstance(market_cap, (int, float)):
            print(f"Market cap: ${market_cap:,}")
        else:
            print(f"Market cap: {market_cap}")
    else:
        print("No data retrieved")
    
    # For full analysis, create analyzer instance with API key
    analyzer = StockAnalyzer(FINANCIAL_MODELING_PREP_API_KEY)
    
    print("\nGenerating comprehensive stock analysis...")
    analysis = analyzer.generate_comprehensive_analysis(cik_ticker, ticker)
    
    # Print just the summary
    print("\n" + "="*50)
    print("EXECUTIVE SUMMARY")
    print("="*50)
    print(analysis.get("summary", "Summary not available"))

def financial_summary(cik_ticker, ticker) -> Optional[str]:
    try:
        from config import FINANCIAL_MODELING_PREP_API_KEY
        # For full analysis, create analyzer instance with API key
        analyzer = StockAnalyzer(FINANCIAL_MODELING_PREP_API_KEY)
        return analyzer.generate_comprehensive_analysis(cik_ticker, ticker)
    except:
        return {"summary": "N/A"}

if __name__ == "__main__":
    #main()
    summary = financial_summary("0000320193", "AAPL")
    print(summary)