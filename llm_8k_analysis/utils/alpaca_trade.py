from alpaca.trading.client import TradingClient
from alpaca.trading.requests import MarketOrderRequest
from alpaca.trading.enums import OrderSide, TimeInForce

trading_client = TradingClient('xxxxxxxxxxxx', 'xxxxxxxxxxxxxxxxxxxxxxxxxx', paper=True)

def get_account():
    """Fetch the account details."""
    return trading_client.get_account()

def submit_market_order(symbol: str, qty: int, side: OrderSide = OrderSide.BUY, time_in_force: TimeInForce = TimeInForce.DAY):
    """Submit a market order."""
    market_order = trading_client.submit_order(
        MarketOrderRequest(
            symbol=symbol,
            qty=qty,
            side=side,
            time_in_force=time_in_force
        )
    )
    return market_order

if __name__ == "__main__":
    account = get_account()
    print(f"Account: {account}")

    market_order = submit_market_order("AAPL", 1)
    print(f"Market Order: {market_order}")
