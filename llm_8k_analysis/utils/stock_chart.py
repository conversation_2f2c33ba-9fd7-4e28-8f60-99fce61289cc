import mplfinance as mpf
import yfinance as yf
import matplotlib.pyplot as plt
from datetime import datetime
import os

def create_stock_chart(ticker, period='1mo', save_dir='charts'):
    """
    Create an enhanced stock chart with moving averages and save to file.
    
    Parameters:
    ticker (str): Stock ticker symbol (e.g., 'AAPL', 'ATNF')
    period (str): Time period for data ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
    save_dir (str): Directory to save the chart image
    
    Returns:
    str: Path to the saved chart file
    """
    
    # Create save directory if it doesn't exist
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # Fetch stock data
    data = yf.Ticker(ticker)
    history = data.history(period=period)
    print(history.tail())
    if history.empty:
        raise ValueError(f"No data found for ticker {ticker}")
    
    # Create custom style for better aesthetics
    custom_style = mpf.make_mpf_style(
        base_mpf_style='nightclouds',
        marketcolors=mpf.make_marketcolors(
            up='#00ff88',      # Bright green for up candles
            down='#ff4444',    # Bright red for down candles
            edge='inherit',
            wick={'up':'#00ff88', 'down':'#ff4444'},
            volume='#4a90e2',  # Blue for volume bars
        ),
        gridcolor='#2a2a2a',
        facecolor='#1a1a1a',
        figcolor='#0f0f0f'
    )
    
    # Add moving averages (only if we have enough data)
    moving_averages = []
    if len(history) >= 7:
        moving_averages.append(
            mpf.make_addplot(history['Close'].rolling(window=7).mean(), 
                           color='#ffaa00', width=1.5, alpha=0.8)
        )
    if len(history) >= 20:
        moving_averages.append(
            mpf.make_addplot(history['Close'].rolling(window=20).mean(), 
                           color='#ff6b6b', width=1.5, alpha=0.8)
        )
    
    # Generate filename with current timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{ticker}_{period}_{timestamp}.png"
    filepath = os.path.join(save_dir, filename)
    
    # Create the plot with enhanced styling
    mpf.plot(
        history,
        type='candle',
        style=custom_style,
        volume=True,
        addplot=moving_averages if moving_averages else None,
        title=f'{ticker.upper()} - Stock Price Analysis ({period.upper()})',
        ylabel='Price ($)',
        ylabel_lower='Volume',
        figsize=(14, 10),
        panel_ratios=(3, 1),  # Make price chart 3x larger than volume chart
        tight_layout=True,
        show_nontrading=False,
        datetime_format='%d/%m',
        xrotation=45,
        update_width_config=dict(
            candle_linewidth=1.2,
            candle_width=0.8,
            volume_linewidth=0.8,
            volume_width=0.8
        ),
        scale_padding={'left': 0.3, 'top': 0.8, 'right': 1.0, 'bottom': 0.3},
        savefig=dict(fname=filepath, dpi=300, bbox_inches='tight', facecolor='#0f0f0f')
    )
    plt.close()  # Close the plot to free memory
    return filepath

if __name__ == "__main__":
    # Example usage
    try:
        chart_path = create_stock_chart('AAPL', period='7d', save_dir='test_charts')
        print(f"Chart saved to {chart_path}")
    except Exception as e:
        print(f"Error creating chart: {e}")