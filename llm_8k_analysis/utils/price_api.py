# https://twelvedata.com/docs#time-series
# https://api.twelvedata.com/time_series?symbol={}&interval=1month&apikey={}

# https://twelvedata.com/docs#quote
# https://api.twelvedata.com/quote?symbol=AAPL&apikey=your_api_key
# https://twelvedata.com/docs#market-movers
# https://api.twelvedata.com/market_movers/stocks?apikey=your_api_key
from requests import Session
import requests
from requests_ratelimiter import  LimiterAdapter
import time
import os
import pandas as pd
from collections import Counter
import yfinance as yf
from config import *
import matplotlib.pyplot as plt
import requests

counter = Counter()
# Apply a rate limit of 5 requests per second to all requests
session_request = Session()
session_request_adapter = LimiterAdapter(per_second=YFINANCE_MAX_RATE_PER_SECOND)
session_request.mount('http://', session_request_adapter)
session_request.mount('https://', session_request_adapter)
# create request header
headers = {'User-Agent': "<NAME_EMAIL>"}

TWELVEDATA_GET_QUOTE_URL = 'https://api.twelvedata.com/quote?symbol={}&apikey={}'
TWELVEDATA_GET_TIME_SERIES_URL = 'https://api.twelvedata.com/time_series?symbol={}&interval={}&apikey={}'
TWELVEDATA_API_KEY = '********************************'

POLYGON_IO_API_KEY = 'pSB_mgtNFgJ8y1qsAIDZZRkXh4TJgCiw'
# https://api.polygon.io/v2/reference/news?limit=10&apiKey=pSB_mgtNFgJ8y1qsAIDZZRkXh4TJgCiw
# https://polygon.io/docs/stocks/get_v2_reference_news
# https://api.polygon.io/vX/reference/ipos?limit=10&apiKey=pSB_mgtNFgJ8y1qsAIDZZRkXh4TJgCiw

FINANCIAL_MODELING_PREP_MARKET_MOVERS_URL = 'https://financialmodelingprep.com/api/v3/stock_market/gainers?apikey={}'

POLYGON_RELATED_COMPANIES = 'https://api.polygon.io/v1/related-companies/AAPL?apiKey=pSB_mgtNFgJ8y1qsAIDZZRkXh4TJgCiw'

POLYGON_IPO_CALENDER = 'https://api.polygon.io/vX/reference/ipos?limit=10&apiKey=pSB_mgtNFgJ8y1qsAIDZZRkXh4TJgCiw'

POLYGON_MARKET_STATUS = 'https://api.polygon.io/v1/marketstatus/now?apiKey=pSB_mgtNFgJ8y1qsAIDZZRkXh4TJgCiw'

# https://api.polygon.io/vX/reference/financials?ticker=NVDA&limit=10&apiKey=pSB_mgtNFgJ8y1qsAIDZZRkXh4TJgCiw
# https://api.polygon.io/v3/reference/tickers/AAPL?apiKey=pSB_mgtNFgJ8y1qsAIDZZRkXh4TJgCiw
class FetchPriceAPI:
    def __init__(self) -> None:
        self.last_stock_info = None

    def get_all_stocks_fmp(self, eodprice:float):
        url = 'https://financialmodelingprep.com/api/v3/stock/full/real-time-price?apikey=bStjIKcA8PCbN4PSAYZ3Mn2K7GwjORQy'
        # Get API response from FMP
        response = requests.get(url)
        response.raise_for_status()
        data_json = response.json()

        # Create DataFrame and filter in one step
        data_df = pd.DataFrame(data_json)
        df_query = f'bidSize > 0 and askPrice > 0 and askSize > 0 and bidPrice > 0 and lastSalePrice > {eodprice} and lastSaleSize > 0 and lastSaleTime > 0 and fmpLast > {eodprice} and lastUpdated > 0'
        data_df = data_df.query(df_query).copy()

        # Optimize timestamp conversion using vectorized operation
        data_df['lastUpdated'] = data_df['lastUpdated'] // 1000

        # Handle percentage change more efficiently
        if self.last_stock_info is None:
            # Save state when the market opens
            self.last_stock_info = data_df.set_index('symbol')['fmpLast']
        
        # Create last price lookup Series with error handling
        last_stock_prices = self.last_stock_info

        # Vectorized percentage change calculation with safe division
        data_df['percentage_change'] = 100 * (
            data_df['fmpLast'] - data_df['symbol'].map(last_stock_prices).fillna(0)
        ) / (data_df['symbol'].map(last_stock_prices).fillna(0) + 1e-10)

        # Rename columns in a single operation
        data_df.rename(columns={
            'askPrice': 'ask', 
            'bidPrice': 'bid', 
            'fmpLast': 'regularMarketPrice', 
            'lastUpdated': 'price_timestamp'
        }, inplace=True)

        return data_df
    
    @staticmethod
    def is_market_open() -> bool:
        # get all companies data
        response = session_request.get(POLYGON_MARKET_STATUS, headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
        response.raise_for_status()
        temp = response.json()
        market_status = {
            'nasdaq': temp['exchanges']['nasdaq'] == 'open',
            'nyse': temp['exchanges']['nyse'] == 'open',
        }
        return market_status['nasdaq'] and market_status['nyse']

    @staticmethod
    def get_quote(ticker: str) -> pd.DataFrame:
        # get all companies data
        response = session_request.get(TWELVEDATA_GET_QUOTE_URL.format(ticker, TWELVEDATA_API_KEY), headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
        response.raise_for_status()
        return response.json()
    
    @staticmethod
    def get_market_movers() -> pd.DataFrame:
        # get all companies data
        response = session_request.get(FINANCIAL_MODELING_PREP_MARKET_MOVERS_URL.format(FINANCIAL_MODELING_PREP_API_KEY), headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
        response.raise_for_status()
        # Convert to DataFrame and sort by change percentage in descending order
        df = pd.DataFrame(response.json())
        df_sorted = df.sort_values('changesPercentage', ascending=False)
        return df_sorted
    
    @staticmethod
    def get_time_series(ticker: str, interval: str) -> pd.DataFrame:
        # get all companies data
        response = session_request.get(TWELVEDATA_GET_TIME_SERIES_URL.format(ticker, interval, TWELVEDATA_API_KEY), headers=headers, timeout=EDGAR_REQUEST_TIMEOUT)
        response.raise_for_status()
        return response.json()
    
    @staticmethod
    def create_stock_change_table(stock_data: pd.DataFrame, output_file='stock_changes.png'):
        """
        Create a table image of stock changes, sorted by change percentage.
        
        Parameters:
        - stock_data (dataframe): Dataframe of stock dictionaries with keys like 'symbol', 'name', 'change', 'changesPercentage'
        - output_file (str): Filename to save the table image
        
        Returns:
        - Saves a PNG image of the sorted stock change table
        """
        
        # Select and format columns, explicitly converting to string
        # table_df = df_sorted[['symbol', 'name', 'price', 'change', 'changesPercentage']]
        table_df = stock_data[['symbol', 'changesPercentage']]
        
        # Convert numeric columns to formatted strings
        def format_number(val):
            if pd.isna(val):
                return 'N/A'
            try:
                return f'{val:.2f}'
            except:
                return str(val)
        
        # Prepare cell text with formatted numbers
        cell_text = []
        for _, row in table_df.iterrows():
            cell_text.append([
                row['symbol'], 
                # row['name'], 
                # format_number(row['price']), 
                # format_number(row['change']), 
                format_number(row['changesPercentage'])
            ])
        
        # Create figure and axis
        # fig, ax = plt.subplots(figsize=(15, len(cell_text)*0.4))
        fig, ax = plt.subplots()
        ax.axis('off')
        
        # Create table
        table = ax.table(
            cellText=cell_text,
            colLabels=table_df.columns,
            cellLoc='center',
            loc='center'
        )
        
        # Style the table
        table.auto_set_font_size(False)
        table.set_fontsize(14)
        table.auto_set_column_width([0, 1])
        table.scale(1, 1.5)  # Adjust column width and row height
        
        # Color the header
        for i, col in enumerate(table_df.columns):
            table[(0,i)].set_facecolor('#f0f0f0')
            table[(0,i)].set_text_props(weight='bold')
        
        # Highlight top performers with color gradient
        for r in range(1, len(cell_text)+1):
            # Green gradient for positive changes
            change_pct = float(cell_text[r-1][1])  # Use the formatted changesPercentage
            green_intensity = min(change_pct / 200, 1)  # Cap at 200% for full green
            table[(r,1)].set_facecolor((0.6, 1-green_intensity, 0.6))
        
        # Adjust layout and save
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        return output_file
    
    @staticmethod
    def get_top_gainers_yfinance(
        eodprice: float, 
        intradaymarketcap: float, 
        eodvolume: float,
        percentchange: float, 
        max_size_per_request: int = 100
    ) -> pd.DataFrame:
        """
        Screen and retrieve top gaining stocks based on specified criteria.
        
        Args:
            eod_price (float): Minimum end-of-day price filter
            market_cap (float): Minimum market capitalization filter
            day_volume (float): Minimum daily trading volume filter
            min_percent_change (float): Minimum percentage change filter
            max_results (int): Maximum number of results to return
        
        Returns:
            pd.DataFrame: Filtered and sorted dataframe of top gaining stocks
        """
        # yf.enable_debug_mode()

        # define categories 
        categories = [
            'aggressive_small_caps',
            'day_gainers',
            'day_losers',
            'growth_technology_stocks',
            'most_actives',
            'most_shorted_stocks',
            'small_cap_gainers',
            'undervalued_growth_stocks',
            'undervalued_large_caps',
        ]

        # Filter top gainers
        gt_price = yf.EquityQuery('gt', ['eodprice', eodprice])
        gt_marketcap = yf.EquityQuery('gt', ['intradaymarketcap', intradaymarketcap])
        gt_dayvolume = yf.EquityQuery('gt', ['eodvolume', eodvolume])
        gt_percentchange = yf.EquityQuery('gt', ['percentchange', percentchange])
        filter = yf.EquityQuery('and', [gt_price, gt_marketcap, gt_dayvolume, gt_percentchange])
        
        # Prepare the screeners
        screener_predefined = yf.Screener(session=session_request)
        screener_equityquery = yf.Screener(session=session_request)
        screener_equityquery.set_body({
            "offset": 0,
            "size": max_size_per_request,
            "sortField": "percentchange",
            "sortType": "desc",
            "quoteType": "equity",
            "query": filter.to_dict(),
            "userId": "",
            "userIdType": "guid"
        })
    
        # Fetch the top gainers from the screener
        response = screener_equityquery.response
        current = response['count']
        total = response['total']
        quotes = response['quotes']

        # Add the first quotes to the dataframe
        df_result = pd.DataFrame(quotes)
        df_result['price_timestamp'] = int(pd.Timestamp.now().timestamp())

        # Fetch the rest of the top gainers
        starting_indexes = range(current, total, max_size_per_request)
        number_of_elements_1 = len(starting_indexes)
        number_of_elements_2 = len(categories)
        for i in range(number_of_elements_1 + number_of_elements_2):
            if i < number_of_elements_2:
                screener_predefined.set_predefined_body(categories[i])
                screener_predefined.patch_body({
                    "size": max_size_per_request,
                    "sortField": "percentchange",
                    "sortType": "DESC"
                })
                quotes = screener_predefined.response['quotes']
            else:
                screener_equityquery.patch_body({
                    "offset": starting_indexes[i - number_of_elements_2],
                })
                response = screener_equityquery.response
                quotes = response['quotes']

            df_quote = pd.DataFrame(quotes)
            if 'symbol' in df_result.columns and 'symbol' in df_quote.columns:
                df_quote['price_timestamp'] = int(pd.Timestamp.now().timestamp())
                df_result = pd.concat([df_result, df_quote[~df_quote['symbol'].isin(df_result['symbol'])]], ignore_index=True)
        # df_result['percentage_change'] = 100* df_result['regularMarketChange'] / df_result['regularMarketOpen']
        df_result['percentage_change'] = df_result['regularMarketChangePercent']
        df_result = df_result[df_result['percentage_change'] > percentchange]
        df_result = df_result[df_result['regularMarketPrice'] > eodprice]
        df_result = df_result[df_result['marketCap'] > intradaymarketcap]
        df_result = df_result[df_result['regularMarketVolume'] > eodvolume]
        df_result = df_result.sort_values('percentage_change', ascending=False)
        # ['PRE' 'POSTPOST' 'REGULAR' 'PREPRE']
        df_result = df_result[df_result['marketState'] == 'REGULAR']
        df_result = df_result[df_result['market'] == 'us_market']
        df_result['api_source'] = 'yfinance'
        # print(df_result[['symbol', 'regularMarketPrice', 'percentage_change', 'regularMarketChangePercent']].head(5))
        df_result.reset_index(drop=True, inplace=True)
        return df_result

    @staticmethod
    def get_top_gainers_alpaca_stocks(eodprice: float):
        # Get the historical prices for a stock
        url = "https://data.alpaca.markets/v1beta1/screener/stocks/movers?top=50"

        API_KEY =  os.environ.get("ALPACA_API_KEY")
        API_SECRET =  os.environ.get("ALPACA_API_SECRET")

        headers = {
            "accept": "application/json",
            "APCA-API-KEY-ID": API_KEY,
            "APCA-API-SECRET-KEY": API_SECRET
        }
        try:
            response = requests.get(url, headers=headers)
            response = response.json()['gainers']
            df = pd.DataFrame(response)
            df = df.rename(columns={'price': 'regularMarketPrice', 'percent_change': 'percentage_change'})
            df['price_timestamp'] = int(pd.Timestamp.now().timestamp())
            df['api_source'] = 'alpaca'
            df = df[df['regularMarketPrice'] > eodprice]
            return df
        except Exception as e:
            print(f"Error fetching top gainers from Alpaca: {e}")

        return pd.DataFrame(columns=['symbol', 'regularMarketPrice', 'percentage_change', 'price_timestamp', 'api_source'])
    
    @staticmethod
    def get_top_gainers(
        eodprice: float, 
        intradaymarketcap: float, 
        eodvolume: float,
        percentchange: float, 
        max_size_per_request: int = 100
    ) -> pd.DataFrame:
        
        top_gainers_yfinance = FetchPriceAPI.get_top_gainers_yfinance(
            eodprice=eodprice, 
            intradaymarketcap=intradaymarketcap, 
            eodvolume=eodvolume, 
            percentchange=percentchange, 
            max_size_per_request=max_size_per_request
        )
        top_gainers_alpaca = FetchPriceAPI.get_top_gainers_alpaca_stocks(eodprice=eodprice)
        # merge top_gainers_alpaca into top_gainers_yfinance only if the symbol is not in top_gainers_yfinance
        top_gainers = pd.concat([top_gainers_yfinance, top_gainers_alpaca[~top_gainers_alpaca['symbol'].isin(top_gainers_yfinance['symbol'])]], ignore_index=True)
        top_gainers = top_gainers.sort_values('percentage_change', ascending=False)
        columns = ['symbol', 'regularMarketPrice', 'percentage_change', 'price_timestamp', 'api_source']
        additional_columns = ['bid', 'ask', 'bidSize', 'askSize']
        if all(col in top_gainers.columns for col in additional_columns):
            columns = columns + additional_columns
        
        return top_gainers[columns]
    
    
    @staticmethod
    def get_top_gainers_alpaca_crypto():
        # Get the historical prices for a stock
        url = "https://data.alpaca.markets/v1beta1/screener/crypto/movers?top=50"

        API_KEY =  os.environ.get("ALPACA_API_KEY"),
        API_SECRET =  os.environ.get("ALPACA_API_SECRET")
        
        headers = {
            "accept": "application/json",
            "APCA-API-KEY-ID": API_KEY,
            "APCA-API-SECRET-KEY": API_SECRET
        }
        try:
            response = requests.get(url, headers=headers)
            response = response.json()['gainers']
            df = pd.DataFrame(response)
            df = df.rename(columns={'price': 'regularMarketPrice', 'percent_change': 'percentage_change'})
            df['price_timestamp'] = int(pd.Timestamp.now().timestamp())
            return df
        except Exception as e:
            print(f"Error fetching top gainers from Alpaca: {e}")

        return pd.DataFrame(columns=['symbol', 'regularMarketPrice', 'percentage_change', 'price_timestamp'])
    
    @staticmethod
    def get_crypto_top_market_cap(max_page: int = 5) -> pd.DataFrame:
        df = pd.DataFrame()
        for i in range(1, max_page + 1):
            try:
                url = f"https://api.coingecko.com/api/v3/coins/markets?order=market_cap_desc&per_page=250&price_change_percentage=24h&vs_currency=usd&page={i}"
                headers = {"accept": "application/json"}

                # send request
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                response = response.json() 

                # Create a DataFrame from the response and add a timestamp
                temp = pd.DataFrame(response)
                temp['price_timestamp'] = int(pd.Timestamp.now().timestamp())

                df = pd.concat([df, temp], ignore_index=True)
                time.sleep(1)
            except Exception as e:
                time.sleep(60)
        # ['id' 'symbol' 'name' 'image' 'current_price' 'market_cap'
        # 'market_cap_rank' 'fully_diluted_valuation' 'total_volume' 'high_24h'
        # 'low_24h' 'price_change_24h' 'price_change_percentage_24h'
        # 'market_cap_change_24h' 'market_cap_change_percentage_24h'
        # 'circulating_supply' 'total_supply' 'max_supply' 'ath'
        # 'ath_change_percentage' 'ath_date' 'atl' 'atl_change_percentage'
        # 'atl_date' 'roi' 'last_updated' 'price_change_percentage_24h_in_currency']
        df = df.rename(columns={'current_price': 'regularMarketPrice', 'price_change_percentage_24h': 'percentage_change'})
        
        # reset index
        df.reset_index(drop=True, inplace=True)
        return df
    
# all_columns = [
# 'language', 'region', 'quoteType', 'typeDisp', 'quoteSourceName',
# 'triggerable', 'customPriceAlertConfidence', 'currency', 'exchange',
# 'esgPopulated', 'tradeable', 'cryptoTradeable', 'hasPrePostMarketData',
# 'firstTradeDateMilliseconds', 'priceHint', 'regularMarketChangePercent',
# 'regularMarketChange', 'regularMarketTime',
# 'regularMarketPrice', 'regularMarketDayHigh', 'regularMarketDayRange',
# 'regularMarketDayLow', 'regularMarketVolume', 'regularMarketPreviousClose',
# 'bid', 'ask', 'bidSize', 'askSize', 'market', 'messageBoardId',
# 'fullExchangeName', 'longName', 'financialCurrency', 'regularMarketOpen',
# 'averageDailyVolume3Month', 'averageDailyVolume10Day',
# 'fiftyTwoWeekLowChange', 'fiftyTwoWeekLowChangePercent',
# 'fiftyTwoWeekRange', 'fiftyTwoWeekHighChange',
# 'fiftyTwoWeekHighChangePercent', 'fiftyTwoWeekChangePercent',
# 'earningsTimestamp', 'earningsTimestampStart', 'earningsTimestampEnd',
# 'earningsCallTimestampStart', 'earningsCallTimestampEnd',
# 'isEarningsDateEstimate', 'trailingAnnualDividendRate',
# 'trailingAnnualDividendYield', 'marketState', 'epsTrailingTwelveMonths',
# 'epsForward', 'epsCurrentYear', 'priceEpsCurrentYear', 'sharesOutstanding',
# 'bookValue', 'fiftyDayAverage', 'fiftyDayAverageChange',
# 'fiftyDayAverageChangePercent', 'twoHundredDayAverage',
# 'twoHundredDayAverageChange', 'twoHundredDayAverageChangePercent',
# 'marketCap', 'forwardPE', 'priceToBook', 'sourceInterval',
# 'exchangeDataDelayedBy', 'exchangeTimezoneName',
# 'exchangeTimezoneShortName', 'gmtOffSetMilliseconds', 'fiftyTwoWeekHigh',
# 'fiftyTwoWeekLow', 'averageAnalystRating', 'shortName', 'displayName',
# 'symbol' 'trailingPE', 'dividendDate',
# 'dividendRate', 'dividendYield',
# 'percentage_change', 'price_timestamp'
# ]

    @staticmethod
    def get_stocks_with_screening_fmp(
        min_market_cap: float = 0, 
        exchanges: list[str] = None
    ) -> pd.DataFrame:
        endpoint = f'https://financialmodelingprep.com/api/v3/stock-screener?apikey={FINANCIAL_MODELING_PREP_API_KEY}&marketCapMoreThan={min_market_cap}&isActivelyTrading=TRUE&isEtf=FALSE&isFund=FALSE'
        response = requests.get(endpoint)
        response.raise_for_status()
        """
        Example response:
        [  {
        "symbol": "NVDA",
        "companyName": "NVIDIA Corporation",
        "marketCap": 3363456600000,
        "sector": "Technology",
        "industry": "Semiconductors",
        "beta": 1.657,
        "price": 137.34,
        "lastAnnualDividend": 0.04,
        "volume": *********,
        "exchange": "NASDAQ Global Select",
        "exchangeShortName": "NASDAQ",
        "country": "US",
        "isEtf": false,
        "isFund": false,
        "isActivelyTrading": true
        }, ...]
        """
        stocks = pd.DataFrame(response.json())
        # order by market cap
        stocks = stocks.sort_values(by='marketCap', ascending=False)
        
        # filter by exchange
        if exchanges:
            stocks = stocks[stocks['exchangeShortName'].isin(exchanges)]
        
        return stocks

    @staticmethod
    def should_buy_stock_by_analist_fmp(
        symbol: str, 
        minimum_buy_threshold_ratio: float
    ) -> bool:
        url = f'https://financialmodelingprep.com/api/v3/analyst-stock-recommendations/{symbol}?apikey={FINANCIAL_MODELING_PREP_API_KEY}'
        response = requests.get(url)
        response.raise_for_status()
        recommendations = response.json()[0]
        # "analystRatingsbuy": 21,
        # "analystRatingsHold": 6,
        # "analystRatingsSell": 0,
        # "analystRatingsStrongSell": 0,
        # "analystRatingsStrongBuy": 11
        # check if the stock has buy/strong buy recommendations
        percent_buy = (recommendations['analystRatingsbuy'] + recommendations['analystRatingsStrongBuy']) / (recommendations['analystRatingsbuy'] + recommendations['analystRatingsHold'] + recommendations['analystRatingsSell'] + recommendations['analystRatingsStrongSell'] + recommendations['analystRatingsStrongBuy'])
        return percent_buy >= minimum_buy_threshold_ratio
    
    @staticmethod
    def stock_change_meets_threshold(
        symbol: str,
        min_percentage_5d_change: float,
        min_percentage_1m_change: float,
        min_percentage_3m_change: float,
        min_percentage_6m_change: float
    ) -> bool:
        url = f'https://financialmodelingprep.com/api/v3/stock-price-change/{symbol}?apikey={FINANCIAL_MODELING_PREP_API_KEY}'
        response = requests.get(url)
        response.raise_for_status()
        stock_change = response.json()[0]
        """
        Example response:
        [
            {
                "symbol": "GOOGL",
                "1D": -1.7605,
                "5D": 11.58519,
                "1M": 5.69321,
                "3M": 24.09335,
                "6M": 7.97008,
                "ytd": 38.9303,
                "1Y": 44.85361,
                "3Y": 31.63272,
                "5Y": 184.72263,
                "10Y": 636.04294,
                "max": 7547.80876
            }
        ]
        """
        return (
            stock_change['1D'] > 0 and 
            stock_change['5D'] >= min_percentage_5d_change and 
            stock_change['1M'] >= min_percentage_1m_change and 
            stock_change['3M'] >= min_percentage_3m_change and 
            stock_change['6M'] >= min_percentage_6m_change
        )
    
    def get_price_to_bid(symbol: str) -> float:
        """
        In stock trading, ask and bid are terms that describe the prices at which a stock is available to buy or sell:

        Ask Price:
            The ask price is the lowest price that a seller is willing to accept for a stock.
            If you are buying a stock, this is the price you’ll pay.
            Example: If the ask price is $50, it means someone is willing to sell the stock for $50.
            Bid Price:
        
        Bid Price:
            The bid price is the highest price that a buyer is willing to pay for a stock.
            If you are selling a stock, this is the price you’ll receive.
            
            Example: If the bid price is $49, it means someone is willing to buy the stock for $49.
            Bid-Ask Spread:
            The difference between the ask price and the bid price is called the bid-ask spread.

        A tight spread (small difference) indicates high liquidity and strong market activity.
        A wide spread (large difference) might occur with less liquid or more volatile stocks.

        Example:
            Bid Price: $99.50
            Ask Price: $100.00
            Spread: $0.50
            If you want to buy, you’d pay $100 (ask price). If you want to sell immediately, you’d receive $99.50 (bid price).
        """
        end_point = f'https://financialmodelingprep.com/api/v3/stock/full/real-time-price/{symbol}?apikey={FINANCIAL_MODELING_PREP_API_KEY}'
        response = requests.get(end_point)
        response.raise_for_status()
        """
        Example response:
        [
        {
            "bidSize": 5,
            "askPrice": 248.06,
            "volume": 31526582,
            "askSize": 1,
            "bidPrice": 248,
            "lastSalePrice": 248,
            "lastSaleSize": 395,
            "lastSaleTime": 1734137906219,
            "fmpLast": 248,
            "lastUpdated": 1734137967723,
            "symbol": "AAPL"
        }
        ]
        """
        response = response.json()[0]
        bid = response['bidPrice']
        ask = response['askPrice']
        spread = ask - bid

        current_price = response['fmpLast']
        last_sale_price = response['lastSalePrice']

        if last_sale_price > ask:
            # last_sale_price is greater than ask price it means the stock is being bought at a higher price
            return round(min(bid + spread / 2, current_price), 2)
        else:
            # last_sale_price is less than ask price it means the stock is being bought at a lower price
            return round(min(bid * 0.995, current_price), 2)
        

    def get_current_price(symbol: str) -> float:
        end_point = f'https://financialmodelingprep.com/api/v3/stock/full/real-time-price/{symbol}?apikey={FINANCIAL_MODELING_PREP_API_KEY}'
        response = requests.get(end_point)
        response.raise_for_status()
        """
        Example response:
        [
        {
            "bidSize": 5,
            "askPrice": 248.06,
            "volume": 31526582,
            "askSize": 1,
            "bidPrice": 248,
            "lastSalePrice": 248,
            "lastSaleSize": 395,
            "lastSaleTime": 1734137906219,
            "fmpLast": 248,
            "lastUpdated": 1734137967723,
            "symbol": "AAPL"
        }
        ]
        """
        response = response.json()[0]
        current_price = response['fmpLast']
        return current_price
